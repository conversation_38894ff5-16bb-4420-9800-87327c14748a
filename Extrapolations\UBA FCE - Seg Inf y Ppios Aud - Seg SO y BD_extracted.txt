================================================================================
PRESENTACIÓN: UBA FCE - Seg Inf y Ppios Aud - Seg SO y BD.pdf
================================================================================
Formato: .pdf
Total Páginas/Diapositivas: 16
Tamaño del archivo: 445,213 bytes
Fecha de extracción: 2025-06-14 21:40:30
Archivo original modificado: 2025-06-14 20:55:31

--- PÁGINA/DIAPOSITIVA 1 ---
Contenido de Texto:
  Seguridad Informática y Principios de Auditoria
Seguridad de Sistemas Operativos y Bases de Datos
Profesor Pablo <PERSON>

Imágenes:
  Imágenes detectadas: 1

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 2 ---
Contenido de Texto:
  Conceptos introductorios – Sistemas Operativos (SO)
Que es un Sistema Operativo (SO)
Es el software de base o programa que actúa como intermediario entre el hardware y el usuario
y se utiliza para gestionar los recursos internos de un dispositivo como ser: Memoria,
Almacenamiento y Periféricos, y permite la coordinación y ejecución de Servicios y Aplicaciones
de usuario final.
Características de un SO Componentes de un SO: Tipos de SO
_Interfaz amigable entre Usuario-HD  Sistema de archivos  Según usuario …
_Administración de recursos eficiente  Interpretación de comandos _multi o mono usuario
_Interactuar con varios dispositivos  Núcleo  Según gestión de tareas …
_Brindar seguridad y protección a los _multi o mono tarea
programas y archivos del dispositivo
 Según gestión de recursos …
_centralizados o distribuidos

Imágenes:
  Imágenes detectadas: 1

Tablas:
  Tabla 1:
    Características de un SO
_Interfaz amigable entre Usuario-HD
_Administración de recursos eficiente
_Interactuar con varios dispositivos
_Brindar seguridad y protección a los
programas y archivos del dispositivo | Componentes de un SO:
 Sistema de archivos
 Interpretación de comandos
 Núcleo | Tipos de SO
 Según usuario …
_multi o mono usuario
 Según gestión de tareas …
_multi o mono tarea
 Según gestión de recursos …
_centralizados o distribuidos

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 3 ---
Contenido de Texto:
  Conceptos introductorios – Base de datos (BD)
Que es una Base de Datos (BD)
Sistema que almacena datos o información perteneciente a un mismo contexto, organizada y
sistematizada lógicamente para su posterior recuperación, análisis o transmisión.
Administración y gestión de BD Organización de BD: Tipos de BD
a través de un DBMS (Database
 Según su estructura  Según su variabilidad…:
Management System)
 Según su jerarquía _estáticas o dinámicas
_Permite el almacenamiento
ordenado y la rápida recuperación de  Según su capacidad de  Según su estructura…
información a través de diferentes interrelación
_jerárquicas, de red,
interfaces digitales
relacionales, multidimensionales
 Según su contenido…
_metadata, texto completo,
directorios, datos especializados

Imágenes:
  Imágenes detectadas: 1

Tablas:
  Tabla 1:
    Administración y gestión de BD
a través de un DBMS (Database
Management System)
_Permite el almacenamiento
ordenado y la rápida recuperación de
información a través de diferentes
interfaces digitales | Organización de BD:
 Según su estructura
 Según su jerarquía
 Según su capacidad de
interrelación | Tipos de BD
 Según su variabilidad…:
_estáticas o dinámicas
 Según su estructura…
_jerárquicas, de red,
relacionales, multidimensionales
 Según su contenido…
_metadata, texto completo,
directorios, datos especializados

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 4 ---
Contenido de Texto:
  Diagrama de flujo de recursos y accesos a datos

Imágenes:
  Imágenes detectadas: 1

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 5 ---
Contenido de Texto:
  Acceso al software y a los datos
Administración y monitoreo de la seguridad
Datos Aplicaciones
Sistema operativo
Red interna
Red perimetral
Seguridad física
Administración de accesos –usuarios de negocio
Administración de accesos –usuarios técnicos
duolC
dadirugeS

Tablas:
  Tabla 1:
    Administración y monitoreo de la seguridad
    Datos Aplicaciones
Sistema operativo
Red interna
Red perimetral
Seguridad física
    Administración de accesos –usuarios de negocio
Administración de accesos –usuarios técnicos

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 6 ---
Contenido de Texto:
  Acceso al software y a los datos
Las aplicaciones, bases de datos y software de base residen en hardware (uno o más equipos físicos - servidores).
Cada servidor posee su propio sistema operativo, aunque la seguridad puede controlarse a través de funcionalidad
común de seguridad (ej. controladores de dominio).
Los sistemas operativos poseen ciertas “cuentas privilegiadas” que pueden otorgar accesos amplios a aplicaciones y
datos. Adicionalmente se pueden crean grupos con permisos amplios que contienen a los usuarios de IT que realizan
funciones técnicas sobre las plataformas (Ej: Domain Admins).
¿Cómo se controla?:
 Restringiendo el acceso a cuentas privilegiadas solamente al personal apropiado
 Monitoreando de forma periódica la actividad de los superusuarios y/o grupos de usuarios administradores
 Aplicando estándares robustos de seguridad y contraseñas adecuadas a las sistemas operativos

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 7 ---
Contenido de Texto:
  Acceso al software y a los datos
Administración de accesos de usuarios técnicos
Todas las aplicaciones, sistemas operativos y bases de datos (también otros componentes de TI tales como firewalls)
poseen un “administrador” o cuentas privilegiadas:
• Windows = Administrador de dominio
• UNIX = Root
• OS/400 = QSECOFR
• Base de datos SQL Server = DBA
• Base de datos Oracle = SYS, SYSTEM
Se requieren medidas de seguridad adicionales (por ejemplo ensobrado de claves – hoy con software) y asignación
específica al personal que desempeña la función.

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 8 ---
Contenido de Texto:
  Estándares de seguridad en SO
Estándares de seguridad recomendados en WINDOWS/LINUX
Se detalla la lista o guía de los principales aspectos de seguridad a considerar en SO
Account Users and Configuration Parameters
Requeriment Description WINDOWS Description LINUX
ROOT accounts
Administrator Account and/or Emergency Account
Accounts with root privileges (or any of its equivalents) created during
The Admin/Administrador/Administrator account, (or any of its
Administrators Accounts installation process, or any generic account with “admin” privileges,
equivalents) or any generic account with “admin” privileges, must have
must have its password stored in the electronic envelope system, or it
its password stored in the electronic envelope system.
must be at least documented by the Information Security Area
Special or service accounts Special or service accounts must be locked or at least documented by the Information Security Area
Default accounts of the operating
All default accounts must be locked or at least documented by the Information Security Area
system.
Limit the use of accounts with blank Accounts: Limit local account use of blank passwords to console logon
passwords only = Enabled
Guest Account Guest account status = Disabled
Test user accounts In case they exist, all test accounts must be locked or at least documented by the Information Security Area.
Configuration of UIDs. Each user must have a UID.
At least one of the following conditions must be complied with:
a.- At /etc/passwd, ROOT shell has to be as /sbin/nologin.
Disable Shell and/or ROOT login N/A
b.- ROOT user login through SSH must be restricted by another setting
in the operating system.

Tablas:
  Tabla 1:
    Requeriment | Description WINDOWS | Description LINUX
    Administrators Accounts | Administrator Account and/or Emergency Account
The Admin/Administrador/Administrator account, (or any of its
equivalents) or any generic account with “admin” privileges, must have
its password stored in the electronic envelope system. | ROOT accounts
Accounts with root privileges (or any of its equivalents) created during
installation process, or any generic account with “admin” privileges,
must have its password stored in the electronic envelope system, or it
must be at least documented by the Information Security Area
    Special or service accounts | Special or service accounts must be locked or at least documented by the Information Security Area | None
    Default accounts of the operating
system. | All default accounts must be locked or at least documented by the Information Security Area | None
    Limit the use of accounts with blank
passwords | Accounts: Limit local account use of blank passwords to console logon
only = Enabled | 
    Guest Account | Guest account status = Disabled | 
    Test user accounts | In case they exist, all test accounts must be locked or at least documented by the Information Security Area. | None
    Configuration of UIDs. | Each user must have a UID. | None
    Disable Shell and/or ROOT login | N/A | At least one of the following conditions must be complied with:
a.- At /etc/passwd, ROOT shell has to be as /sbin/nologin.
b.- ROOT user login through SSH must be restricted by another setting
in the operating system.

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 9 ---
Contenido de Texto:
  Estándares de seguridad en SO
Estándares de seguridad recomendados en WINDOWS/LINUX (Cont.)
Account Blocking and Password Policies
Requeriment Description Value
Enforce password history This establishes the number of different passwords for one user before user may repeat them. 10
Maximum password age This establishes the time users may keep their passwords. 90 days
Minimum password age This establishes the time users may keep their passwords before changing them. 1 day
Minimum password length This establishes the minimum of characters that a password may have. 9 or more characters.
Password must meet complexity requirements This imposes the use of non-alphabetic characters for passwords. Enabled
Store password using reversible encryption for all
Users’ passwords may be obtained in plain text. Disabled
users in the domain
Account lockout duration This establishes how much time an account will be blocked. 30 minutes
Account lockout threshold This establishes the number of failed attempts before an account is blocked. 5
Reset account lockout counter after This establishes the time after which the counter will be reset. 30 minutes
Prompt user to change password before expiration This establishes the time after which user is warned to change his/her password before expiration. 10 days

Tablas:
  Tabla 1:
    Requeriment | Description | Value
    Enforce password history | This establishes the number of different passwords for one user before user may repeat them. | 10
    Maximum password age | This establishes the time users may keep their passwords. | 90 days
    Minimum password age | This establishes the time users may keep their passwords before changing them. | 1 day
    Minimum password length | This establishes the minimum of characters that a password may have. | 9 or more characters.
    Password must meet complexity requirements | This imposes the use of non-alphabetic characters for passwords. | Enabled
    Store password using reversible encryption for all
users in the domain | Users’ passwords may be obtained in plain text. | Disabled
    Account lockout duration | This establishes how much time an account will be blocked. | 30 minutes
    Account lockout threshold | This establishes the number of failed attempts before an account is blocked. | 5
    Reset account lockout counter after | This establishes the time after which the counter will be reset. | 30 minutes
    Prompt user to change password before expiration | This establishes the time after which user is warned to change his/her password before expiration. | 10 days

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 10 ---
Contenido de Texto:
  Estándares de seguridad en SO
Estándares de seguridad recomendados en WINDOWS/LINUX (Cont.)
Audit
Audit Events Success Failed No Auditing
Audit account logon events √ √
Audit account management √ √
Audit directory service access √
Audit logon events √ √
Audit object access √
Audit policy changes √ √
Audit privilege use √ √
Audit process tracking √
Monitoring – Logs
In addition to the configurations mentioned above, other necessary configurations will be performed for reporting logs
into the organizational monitoring systems. Logs must be sent to the corresponding log collector.
Antivirus
Server must be protected by the organizational antivirus solution, for such purpose, the pertinent agent will be installed
and associated with the corporate antivirus console.

Tablas:
  Tabla 1:
    Audit Events | Success | Failed | No Auditing
    Audit account logon events
Audit account management
Audit directory service access
Audit logon events
Audit object access
Audit policy changes
Audit privilege use
Audit process tracking | √
√
√
√
√ | √
√
√
√
√
√ | √
√

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 11 ---
Contenido de Texto:
  Consideraciones de seguridad en Bases de datos
Los principales objetivos de la seguridad en base de datos son:
 Proteger la confidencialidad, integridad y disponibilidad de la BD
 Proteger los datos contra ciberataques y actores malintencionados
 Proteger los datos contra el acceso no autorizado, la manipulación o las filtraciones de datos
Medidas de seguridad a considerar para administrar BD:
 Autenticación
 Controles de acceso
 Cifrado
 Auditorías de seguridad periódicas
 Políticas, procedimientos y prácticas de gestión de la seguridad de la información
 Procesos para la gestión de usuarios y privilegios

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 12 ---
Contenido de Texto:
  Consideraciones de seguridad en Bases de datos
Recomendaciones de control para BD:
 Aplicar el principio del menor privilegio
 Revisar y revocar regularmente los privilegios que ya no sean necesarios
 Implementar un sistema de Gestión de Acceso Privilegiado (PAM) automatizado
 Detectar los intentos de acceso inadecuado con rapidez
 Controlar consultas SQL en tiempo real
 Realizar auditorías regulares para garantizar el cumplimiento de las políticas de seguridad

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 13 ---
Contenido de Texto:
  Estrategia de seguridad de bases de datos
Una estrategia de seguridad de bases de datos bien definida e implementada debe incluir la definición de políticas y
controles que ayuden a mitigar múltiples amenazas internas o externas.
La mejor estrategia es aplicar un marco integrado de controles de seguridad en BD que se pueda implementar
fácilmente en los distintos niveles de seguridad.
Algunos de los controles más utilizados para proteger las bases de datos son:
Controles de Controles Controles Controles a Controles a
“evaluación” “detectivos” “preventivos” “nivel de datos” “nivel de usuarios”
• Configuración de la • Auditoria de • Cifrado • Seg de fila • Autenticación de
BD accesos de Usuario • Ocultamiento • Seg de campo usuarios y
• Versión y parches • Reporte de • Enmascaramiento • Seg de aplicación contraseñas
de la BD comportamientos • Definición de Roles
• Diccionario de la BD
• Definición de anómalos y Privilegios
privilegios • Reporte de • Autenticación con
• Datos confidenciales amenazas protocolo Kerberos

Imágenes:
  Imágenes detectadas: 5

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 14 ---
Contenido de Texto:
  Estándares de seguridad en BD
Estándares de seguridad recomendados en ORACLE/SQL
Se detalla la lista o guía de los principales aspectos de seguridad a considerar en BD
General Provisions
Requirement Description
Installation Installation must not be done in a Domain Controller.
NOTES: It is necessary to check periodically “security alert” site and test patches in test environments before replicating the same in
Production environments
Users and Configuration Parameters
Requirement Description ORACLE
SYS account (or any of its equivalents), as well as all special accounts with “admin”
DBA accounts privileges, must have its password stored in the electronic envelope system, or it must
be at least documented by the Information Security Area
Default accounts must be locked and expired or must be at least documented by
User Accounts created by default in the Database
the Information Security Area
All test accounts must be locked or must be at least documented by the Information
Test User Accounts
Security Area
Those service accounts whose password may not be changed due to their
Service User Accounts
functionality/nature must be documented by the Information Security Area

Tablas:
  Tabla 1:
    Requirement | Description
    Installation | Installation must not be done in a Domain Controller.

  Tabla 2:
    Requirement | Description ORACLE
    DBA accounts | SYS account (or any of its equivalents), as well as all special accounts with “admin”
privileges, must have its password stored in the electronic envelope system, or it must
be at least documented by the Information Security Area
    User Accounts created by default in the Database | Default accounts must be locked and expired or must be at least documented by
the Information Security Area
    Test User Accounts | All test accounts must be locked or must be at least documented by the Information
Security Area
    Service User Accounts | Those service accounts whose password may not be changed due to their
functionality/nature must be documented by the Information Security Area

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 15 ---
Contenido de Texto:
  Estándares de seguridad en BD
Estándares de seguridad recomendados en ORACLE (Cont.)
The following table lists the minimum configurations required
Parameters Value
AUDIT_TRAIL “DB” or “DB, EXTENDED”
AUDIT_SYS_OPERATIONS TRUE
SQL92_SECURITY TRUE
FALSE
07_DICTIONARY_ACCESSIBILITY
*In 19c and next versions, this setting doesn’t exist
REMOTE_OS_ROLES FALSE
REMOTE_LOGIN_PASSWORDFILE EXCLUSIVE
RESOURCE_LIMIT TRUE
TRUE
SEC_CASE_SENSITIVE_LOGON
*In 10g and previous versions, this setting doesn’t exist
FALSE
SEC_RETURN_SERVER_RELEASE_BANNER
*In 10g and previous versions, this setting doesn’t exist
NULL
UTL_FILE_DIR
*In 18c and next versions, this setting doesn’t exist
FALSE or its equivalent value to avoid predetermination with an asterisk ('*'). It may
_TRACE_FILES_PUBLIC
only include protected directories.
5
SEC_MAX_FAILED_LOGIN_ATTEMPTS
*In 10g and previous versions, this setting doesn’t exist

Tablas:
  Tabla 1:
    Parameters | Value
    AUDIT_TRAIL | “DB” or “DB, EXTENDED”
    AUDIT_SYS_OPERATIONS | TRUE
    SQL92_SECURITY | TRUE
    07_DICTIONARY_ACCESSIBILITY | FALSE
*In 19c and next versions, this setting doesn’t exist
    REMOTE_OS_ROLES | FALSE
    REMOTE_LOGIN_PASSWORDFILE | EXCLUSIVE
    RESOURCE_LIMIT | TRUE
    SEC_CASE_SENSITIVE_LOGON | TRUE
*In 10g and previous versions, this setting doesn’t exist
    SEC_RETURN_SERVER_RELEASE_BANNER | FALSE
*In 10g and previous versions, this setting doesn’t exist
    UTL_FILE_DIR | NULL
*In 18c and next versions, this setting doesn’t exist
    _TRACE_FILES_PUBLIC | FALSE or its equivalent value to avoid predetermination with an asterisk ('*'). It may
only include protected directories.
    SEC_MAX_FAILED_LOGIN_ATTEMPTS | 5
*In 10g and previous versions, this setting doesn’t exist

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 16 ---
Contenido de Texto:
  Estándares de seguridad en BD
Estándares de seguridad recomendados en ORACLE (Cont.)
Passwords and Login Configurations
Parameters Value
FAILED_LOGIN_ATTEMPS 5
PASSWORD_LOCK_TIME UNLIMITED
PASSWORD_LIFE_TIME 90 days
PASSWORD_GRACE_TIME 5
PASSWORD_REUSE_MAX 10
PASSWORD_REUSE_TIME 300
All users: Audit Option/Success/Failure SUCCESS & FAILURE = BY ACCESS
NOTES: Production profiles must be set up with the following configuration. In case of certain profiles containing service accounts, or
accounts with effects on automated processes, these settings will not be complied with. These profiles must be at least documented by the
Information Security Area
Monitoring / LOGs
In addition to the configurations mentioned above, other necessary configurations will be performed for reporting logs
into the organizational monitoring systems. In this respect, you must configure and check that logs are sent to the log
collector

Tablas:
  Tabla 1:
    Parameters | Value
    FAILED_LOGIN_ATTEMPS
PASSWORD_LOCK_TIME
PASSWORD_LIFE_TIME
PASSWORD_GRACE_TIME
PASSWORD_REUSE_MAX
PASSWORD_REUSE_TIME
All users: Audit Option/Success/Failure | 5
UNLIMITED
90 days
5
10
300
SUCCESS & FAILURE = BY ACCESS

--------------------------------------------------
