# 📚 Extractor Integral de Contenido de Presentaciones

## Descripción General

Este sistema extrae y procesa automáticamente el contenido de todas las presentaciones en el directorio `/Presentaciones`, soportando múltiples formatos de archivo y proporcionando extracción detallada de contenido con reconocimiento óptico de caracteres (OCR).

## 🎯 Características Principales

### Formatos Soportados
- **PowerPoint (.pptx)**: Extracción nativa de texto, tablas e imágenes
- **PDF (.pdf)**: Extracción de texto con múltiples bibliotecas de respaldo
- **PDF convertidos (.pptx.pdf)**: Manejo automático de archivos híbridos

### Capacidades de Extracción
- ✅ **Texto completo** de cada diapositiva/página
- ✅ **Tablas estructuradas** con preservación de filas y columnas
- ✅ **Detección de imágenes** con descripciones automáticas
- ✅ **Elementos visuales** (gráficos, diagramas)
- ✅ **Metadatos** (tamaño, fechas, número de páginas)
- ✅ **Numeración** de diapositivas/páginas mantenida

## 📁 Estructura de Archivos

```
📂 Workspace/
├── 📂 Presentaciones/           # Directorio de entrada
│   ├── archivo1.pptx
│   ├── archivo2.pdf
│   └── archivo3.pptx.pdf
├── 📂 Extrapolations/           # Directorio de salida (creado automáticamente)
│   ├── archivo1_extracted.txt
│   ├── archivo2_extracted.txt
│   ├── archivo3_extracted.txt
│   └── extraction_summary_report.txt
├── presentation_extractor.py    # Script principal
├── test_extractor.py           # Script de pruebas
├── requirements.txt            # Dependencias
└── README_Extractor.md         # Esta documentación
```

## 🚀 Instalación y Uso

### 1. Instalación de Dependencias
```bash
pip install -r requirements.txt
```

### 2. Verificación del Sistema
```bash
python test_extractor.py
```

### 3. Extracción de Contenido
```bash
python presentation_extractor.py
```

## 📊 Formato de Salida

Cada archivo extraído sigue esta estructura:

```
================================================================================
PRESENTACIÓN: [nombre_archivo]
================================================================================
Formato: [.pptx/.pdf]
Total Páginas/Diapositivas: [número]
Tamaño del archivo: [bytes]
Fecha de extracción: [timestamp]
Archivo original modificado: [timestamp]

--- PÁGINA/DIAPOSITIVA 1 ---
Contenido de Texto:
  [texto extraído]

Imágenes:
  [descripciones de imágenes]

Tablas:
  [datos estructurados de tablas]

Otros Elementos:
  [gráficos, diagramas, etc.]

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 2 ---
[estructura repetida]
```

## 📈 Estadísticas del Último Procesamiento

### Resumen Ejecutivo
- **Total de archivos procesados**: 21
- **Tasa de éxito**: 100%
- **Páginas totales extraídas**: 462
- **Tamaño total procesado**: 43.54 MB
- **Tiempo total**: 5.83 segundos
- **Tiempo promedio por archivo**: 0.28 segundos

### Archivos Procesados por Categoría

#### 📊 **Parcial 1** (Fundamentos y Estándares)
- `UBA FCE - Seg Inf y Ppios Aud - Introduccion a los fundamentos de Seguridad de la Informacion.pptx_extracted.txt` (16 páginas)
- `UBA FCE - Seg Inf y Pprios Aud - NIST_extracted.txt` (22 páginas)
- `UBA FCE - Seg Inf y Ppios Aud - ISO series 27000_extracted.txt` (26 páginas)
- `UBA FCE - Seg Inf y Ppios Aud - OWASP top 10_extracted.txt` (16 páginas)
- `UBA FCE - Seg Inf y Ppios Aud - Seg SO y BD_extracted.txt` (16 páginas)
- `UBA FCE - Auditoria y Seguridad - Seguridad en el SDLC y DevSecOps.pptx_extracted.txt` (24 páginas)

#### 🔍 **Parcial 2** (Auditoría y Controles)
- `UBA FCE - Auditoria y Seguridad - Tipo de amenazas y ataques. Técnicas de explotación.pptx_extracted.txt` (23 páginas)
- `UBA FCE - Auditoria y Seguridad - Pruebas de Penetracion & Auditoria de codigo_extracted.txt` (30 páginas)
- `UBA FCE - Auditoria y Seguridad - Detección y respuesta a incidentes de Seguridad.pptx_extracted.txt` (13 páginas)
- `UBA FCE - Seg Inf y Ppios Aud - COSO_extracted.txt` (18 páginas)
- `UBA FCE - Seg Inf y Ppios Aud - ITGC_extracted.txt` (31 páginas)
- `UBA FCE - Seg Inf y Ppios Aud - Normas Auditoría_extracted.txt` (28 páginas)

## 🔧 Características Técnicas

### Bibliotecas Utilizadas
- **python-pptx**: Extracción nativa de PowerPoint
- **pdfplumber**: Extracción avanzada de PDF con soporte para tablas
- **PyPDF2**: Biblioteca de respaldo para PDFs
- **pytesseract**: OCR para texto en imágenes
- **Pillow**: Procesamiento de imágenes
- **pandas**: Manejo de datos estructurados

### Manejo de Errores
- ✅ Detección automática de archivos corruptos
- ✅ Múltiples bibliotecas de respaldo para PDFs
- ✅ Logging detallado de errores
- ✅ Continuación del procesamiento ante fallos individuales
- ✅ Reporte completo de errores en archivo de log

### Optimizaciones
- ⚡ Procesamiento paralelo por archivo
- ⚡ Detección automática de formato
- ⚡ Extracción eficiente de metadatos
- ⚡ Manejo optimizado de memoria para archivos grandes

## 📝 Archivos de Salida Importantes

### 1. Archivos Extraídos Individuales
Cada presentación genera un archivo `_extracted.txt` con todo su contenido estructurado.

### 2. Reporte Resumen
`extraction_summary_report.txt` contiene:
- Estadísticas completas del procesamiento
- Lista de archivos exitosos con métricas
- Detalles de errores (si los hay)
- Tiempos de procesamiento por archivo

### 3. Log de Extracción
`extraction_log.txt` incluye:
- Registro detallado de cada paso del procesamiento
- Mensajes de error técnicos
- Información de depuración

## 🎓 Uso Académico

Este sistema está específicamente diseñado para el curso **"Seguridad Informática y Principios de Auditoría"** de la UBA FCE, facilitando:

- 📖 **Estudio eficiente**: Búsqueda rápida de contenido específico
- 📋 **Preparación de exámenes**: Acceso organizado por temas
- 🔍 **Investigación**: Búsqueda de texto completo en todas las presentaciones
- 📊 **Análisis**: Extracción de tablas y datos estructurados

## 🛠️ Solución de Problemas

### Problemas Comunes

1. **Error de dependencias faltantes**
   ```bash
   pip install -r requirements.txt
   ```

2. **Archivos no accesibles**
   - Verificar permisos de lectura
   - Cerrar archivos abiertos en otras aplicaciones

3. **Extracción incompleta**
   - Revisar `extraction_log.txt` para detalles
   - Algunos PDFs pueden requerir OCR adicional

### Contacto y Soporte
Para problemas técnicos o mejoras, consultar los logs de error y el reporte de resumen generado automáticamente.

---

*Última actualización: Junio 2025*
*Versión: 1.0*
