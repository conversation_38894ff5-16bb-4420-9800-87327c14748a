================================================================================
PRESENTACIÓN: UBA FCE - Seg Inf y Ppios Aud - OWASP top 10.pdf
================================================================================
Formato: .pdf
Total Páginas/Diapositivas: 16
Tamaño del archivo: 735,571 bytes
Fecha de extracción: 2025-06-14 21:40:30
Archivo original modificado: 2025-06-14 20:55:25

--- PÁGINA/DIAPOSITIVA 1 ---
Contenido de Texto:
  Seguridad Informática y
Principios de Auditoría
OWASP top 10
Profesor <PERSON> Gil

Imágenes:
  Imágenes detectadas: 1

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 2 ---
Contenido de Texto:
  OWASP
El Proyecto Abierto de Seguridad en Aplicaciones Web (OWASP por sus
siglas en inglés) es una comunidad abierta dedicada a permitir que las
organizaciones desarrollen, adquieran y mantengan aplicaciones y APIs
en las que se pueda confiar.
Aunque el objetivo original del proyecto OWASP Top 10 fue
simplemente concientizar sobre los procesos seguros de desarrollo de
software, se ha convertido en un standard de seguridad de facto
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 2

Imágenes:
  Imágenes detectadas: 1

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 3 ---
Contenido de Texto:
  OWASP
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 3

Imágenes:
  Imágenes detectadas: 2

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 4 ---
Contenido de Texto:
  A1:2021 Pérdida de control de acceso
Las restricciones sobre lo que los usuarios autenticados pueden hacer
no se aplican correctamente. Los atacantes pueden explotar estos
defectos para acceder, de forma no autorizada, a funcionalidades y/o
datos, cuentas de otros usuarios, ver archivos sensibles, modificar
datos, cambiar derechos de acceso y permisos, etc.
https://example.com/app/getappInfo
https://example.com/app/admin_getappInfo
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 4

Imágenes:
  Imágenes detectadas: 1

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 5 ---
Contenido de Texto:
  A2:2021 Fallas criptográficas
Muchas aplicaciones web y APIs no protegen adecuadamente datos
sensibles, tales como información financiera, de salud o Información
Personalmente Identificable (PII). Los atacantes pueden robar o
modificar estos datos protegidos inadecuadamente para llevar a cabo
fraudes con tarjetas de crédito, robos de identidad u otros delitos. Los
datos sensibles requieren métodos de protección adicionales, como el
cifrado en almacenamiento y tránsito
Recomendaciones:
● No almacene datos sensibles innecesariamente. Descártelos tan
pronto como sea posible o utilice un sistema de tokenizacion que
cumpla con PCI DSS. Los datos que no se almacenan no pueden ser
robados
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 5

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 6 ---
Contenido de Texto:
  A3:2021 Inyección
Las fallas de inyección, como SQL, NoSQL, OS o LDAP ocurren cuando se
envían datos no confiables a un intérprete, como parte de un comando o
consulta. Los datos dañinos del atacante pueden engañar al intérprete para
que ejecute comandos involuntarios o acceda a los datos sin la debida
autorización.
Una aplicación es vulnerable a ataques de este tipo cuando:
• Los datos suministrados por el usuario no son validados, filtrados o
sanitizados por la aplicación.
• Se invocan consultas dinámicas o no parametrizadas, sin codificar los
parámetros de forma acorde al contexto.
• Se utilizan datos dañinos dentro de los parámetros de búsqueda en
consultas Object-Relational Mapping (ORM), para extraer registros
adicionales sensibles.
• Los datos dañinos se usan directamente o se concatenan, de modo que el
SQL o comando resultante contiene datos y estructuras con consultas
dinámicas, comandos o procedimientos almacenados.
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 6

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 7 ---
Contenido de Texto:
  A3:2021 Inyección
https://www.youtube.com/watch?v=yAOldODQCN4&t=9s
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 7

Imágenes:
  Imágenes detectadas: 1

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 8 ---
Contenido de Texto:
  A4:2021 Diseño inseguro
Riesgos relacionados con el diseño y las fallas de arquitectura
CWE-209 Generación de mensaje de error que contiene información confidencial
CWE-256 Almacenamiento desprotegido de credenciales
CWE-501 Violación de las fronteras de confianza
CWE-522 Credenciales protegidas insuficientemente
Recomendaciones:
● Modelado de amenazas
● Patrones de diseño seguro
● Arquitecturas de referencia
● “Mover a la izquierda” del proceso de desarrollo las actividades de
seguridad (seguridad por diseño). SSDLC
● Modelo de Madurez para el Aseguramiento del Software (SAMM)
https://owaspsamm.org/
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 8

Imágenes:
  Imágenes detectadas: 1

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 9 ---
Contenido de Texto:
  A5:2021 Configuración de seguridad incorrecta
La configuración de seguridad incorrecta es un problema muy común y
se debe en parte a establecer la configuración de forma manual, ad hoc
o por omisión (o directamente por la falta de configuración).
Ejemplos:
• Funciones innecesarias habilitadas o instaladas (falta de hardening
en cualquier parte del stack o permisos configurados
incorrectamente)
• S3 buckets abiertos
• Cabeceras HTTP mal configuradas,
• Mensajes de error demasiado informativos
• Falta de parches y actualizaciones
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 9

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 10 ---
Contenido de Texto:
  A6:2021 Componentes vulnerables y desactualizados
Los componentes como bibliotecas, frameworks y otros módulos se ejecutan con los
mismos privilegios que la aplicación. Si se explota un componente vulnerable (también
aplica a OS, DBMS, web servers, etc), el ataque puede provocar una pérdida de datos o
tomar el control del servidor. Las aplicaciones y API que utilizan componentes con
vulnerabilidades conocidas pueden debilitar las defensas de las aplicaciones y permitir
diversos ataques e impactos.
https://www.youtube.com/watch?v=XxC5XblLhPw&t=110s
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 10

Imágenes:
  Imágenes detectadas: 1

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 11 ---
Contenido de Texto:
  A7:2021 Fallas de identificación y autenticación
Las funciones de la aplicación relacionadas a autenticación y gestión de
sesiones son implementadas incorrectamente, permitiendo a los
atacantes comprometer usuarios y contraseñas, token de sesiones, o
explotar otras fallas de implementación para asumir la identidad de
otros usuarios (temporal o permanentemente).
Pueden existir debilidades de autenticación si la aplicación:
● Permite ataques de fuerza bruta y/o ataques automatizados.
● Permite contraseñas por defecto, débiles o muy conocidas, como “Password1”,
“Contraseña1” o “admin/admin”
● Almacena las contraseñas en texto claro o cifradas con métodos de hashing débiles
● No posee autenticación multi-factor o fue implementada de forma ineficaz.
● Expone Session IDs en las URL, no la invalida correctamente o no la rota
satisfactoriamente luego del cierre de sesión o de un periodo de tiempo determinado
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 11

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 12 ---
Contenido de Texto:
  A7:2021 Fallas de identificación y autenticación
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 12

Imágenes:
  Imágenes detectadas: 2

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 13 ---
Contenido de Texto:
  A8:2021 Fallas en el software y en la integridad de
los datos
Código e infraestructura no protegidos contra alteraciones (integridad)
Ej. cuando una aplicación depende de plugins, bibliotecas o módulos de fuentes,
repositorios o redes de entrega de contenidos (CDN) no confiables. Un pipeline
CI/CD inseguro puede conducir a accesos no autorizados, la inclusión de código
malicioso o el compromiso del sistema en general. También es común que las
actualizaciones de aplicaciones no posean las mismas verificaciones de
integridad que la instalación original.
Recomendaciones:
● Firmas digitales para asegurar proveniencia del software o los datos
● Bibliotecas y dependencias de repositorios confiables
● Herramientas de análisis de componentes de terceros
● Proceso de revisión de cambios de código o configuraciones (peer review,
SAST, DAST)
● Seguridad pipeline CI/CD
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 13

Imágenes:
  Imágenes detectadas: 1

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 14 ---
Contenido de Texto:
  A8:2021 Fallas en el software y en la integridad de
los datos
https://www.youtube.com/watch?v=pNlxH07iRZY&t=6s
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 14

Imágenes:
  Imágenes detectadas: 1

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 15 ---
Contenido de Texto:
  A9:2021 Fallas en el registro y monitoreo
El registro y monitoreo insuficiente, junto a la falta de respuesta ante
incidentes permiten a los atacantes mantener el ataque en el tiempo,
pivotear a otros sistemas y manipular, extraer o destruir datos. Algunos
estudios muestran que el tiempo de detección de una brecha de
seguridad puede ser mayor a 200 días, siendo típicamente detectado
por terceros en lugar de por procesos internos.
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 15

Imágenes:
  Imágenes detectadas: 1

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 16 ---
Contenido de Texto:
  A10:2021 Falsificación de solicitudes del lado del
servidor
Las fallas de SSRF ocurren cuando una aplicación web está obteniendo un
recurso remoto sin validar la URL proporcionada por el usuario. Permite que
un atacante coaccione a la aplicación para que envíe una solicitud falsificada a
un destino inesperado, incluso cuando está protegido por un firewall, VPN u
otro tipo de lista de control de acceso a la red (ACL).
La gravedad de SSRF es cada vez mayor debido a los servicios en la nube y la
complejidad de las arquitecturas.
Recomendaciones:
● Capa de red: segmentar la funcionalidad de acceso a recursos remotos en
redes separadas; políticas de firewall de “denegar por defecto”
● Capa de aplicación: sanitizar y validad todos los datos de entrada;
whitelisting de URLs; no enviar respuestas en formato “crudo”;
deshabilitar redirecciones HTTP.
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 16

Imágenes:
  Imágenes detectadas: 1

--------------------------------------------------
