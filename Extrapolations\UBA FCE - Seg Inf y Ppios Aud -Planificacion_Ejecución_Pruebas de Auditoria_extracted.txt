================================================================================
PRESENTACIÓN: UBA FCE - Seg Inf y Ppios Aud -Planificacion_Ejecución_Pruebas de Auditoria.pptx
================================================================================
Formato: .pptx
Total Páginas/Diapositivas: 33
Tamaño del archivo: 316,967 bytes
Fecha de extracción: 2025-06-14 21:40:30
Archivo original modificado: 2025-06-14 20:56:20

--- PÁGINA/DIAPOSITIVA 1 ---
Contenido de Texto:
  Seguridad Informática y Principios de AuditoríaProceso de Auditoría
  Profesor Pablo Gil

Imágenes:
  Imagen detectada: Picture 3

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 2 ---
Contenido de Texto:
  Agenda
  Proceso de Auditoría: 
Definición de alcance, pruebas de control y sustantivas
Entendimiento, evaluación y validación del ambiente de control interno
Relevamiento y evaluación del diseño de controles
Pruebas de controles (efectividad operativa de los controles)
Informes de auditoria
  Slide 2

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 3 ---
Contenido de Texto:
  Seguridad Informática y Principios de AuditoríaDefinición de alcance, pruebas de control y sustantivas
  Profesor Pablo Gil

Imágenes:
  Imagen detectada: Picture 3

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 4 ---
Contenido de Texto:
  Slide 4
  Definición de alcance

Tablas:
  Tabla 1:
    Auditoría Externa | Auditoría Interna
    Materialidad | Plan basado en análisis de riesgos
    Análisis cuantitativo y cualitativo | Consideración de aspectos de foco del directorio y de la alta gerencia
    Procedimientos de impredecibilidad | Seguimiento observaciones períodos anteriores

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 5 ---
Contenido de Texto:
  Slide 5
  Enfoque del trabajo de auditoría interna
  Basado en: requerimientos normativos, riesgo y aspectos operativos importantes para la dirección

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 6 ---
Contenido de Texto:
  Slide 6
  Enfoque del trabajo de auditoría externa
  Más controles = menos riesgo = menor cantidad de procedimientos sustantivos
  aturaleza
  lcance
  portunidad
  N
  A
  O

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 7 ---
Contenido de Texto:
  Slide 7
  Procedimientos analíticos - Tipos
  Análisis de tendencias
  Análisis de ratios
  Pruebas de razonabilidad
  Escaneo analítico
  Análisis de regresión
  EJ: endeudamiento (P/PN), solvencia (A/P), liquidez (AC/PC), tesorería (AC-inv/PC)
  Evolución de ingresos, saldos de cuentas, etc
  Ej: PxQ ventas estimadas vs saldo contable
  Similar razonabilidad pero con métodos estadísticos y relación entre variables
  Revisión de transacciones en búsqueda de valores inusuales
  ‘el análisis de ratios significativos y tendencias incluida la investigación resultante de fluctuaciones y relaciones que no concuerdan con otra información relevante o se desvían de los montos previstos’. (ISA 520)

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 8 ---
Contenido de Texto:
  Slide 8
  Pruebas sustantivas de detalle
  Comprenden la examinación de evidencia física / documentación soporte de ítems que componen el saldo de una cuenta patrimonial o de resultados
  Examinación física (ej inventario)
 Recálculo 
 Reconciliación
 Confirmación (con terceras partes ej bancos, abogados, clients)
 Re-ejecución
 Pruebas de corte
Aplicación de muestros estadísticos para tamaños de muestra
En ciertos casos puede utilizarse software de auditoria para ejecutar las pruebas

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 9 ---
Contenido de Texto:
  Seguridad Informática y Principios de AuditoríaEntendimiento, evaluación y validación del ambiente de control interno
  Profesor Pablo Gil

Imágenes:
  Imagen detectada: Picture 10

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 10 ---
Contenido de Texto:
  A  Focus on Control Activities
  El “ciclo de confort” para la auditoría de control interno

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 11 ---
Contenido de Texto:
  Características¿Cómo se documenta?En general, la documentación del control interno de un proceso (IT o negocio) puede contar con los siguientes elementos: Diagrama de flujo del proceso Narrativa del proceso Organigrama de las áreas que intervienen en el proceso Matriz de riesgos y controles Análisis de manuales y procedimientos del proceso
  A  Focus on Control Activities
  Entendimiento: documentación del control interno

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 12 ---
Contenido de Texto:
  Al momento de documentar un control, deben considerarse los siguientes aspectos
  A  Focus on Control Activities
  Documentación del control interno
  Inputs son utilizados para realizar el control (inputs/proceso/outputs)?
  Realiza el control (tener en cuenta segregación de funciones)?
  El control es ejecutado?
  Evidencia se deja luego de realizar el control?
  Se resuelven las excepciones?
  QUIEN
  QUE
  DONDE
  QUE
  COMO
  Es que se realiza el control (tiempo/frecuencia)?
  CUANDO

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 13 ---
Contenido de Texto:
  Flujogramas
  A  Focus on Control Activities
  Documentación del control interno

Tablas:
  Tabla 1:
    Objetivos | El proceso debe ser documentado, con un nivel de detalle que permita  efectuar un seguimiento a las actividades y/o tareas que lo comprenden, como también la fácil identificación de  los controles que posee.
    Enfoque | Debe ser lo suficientemente claro para comprender el proceso sin necesidad de explicaciones adicionales.

Debe contener todos los documentos, archivos de datos, registros contables, reportes importantes y sistemas que intervienen.

Para realizar el flujograma existen distintas herramientas (ej Visio)

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 14 ---
Contenido de Texto:
  Documentación del control interno
  Narrativas

Tablas:
  Tabla 1:
    Objetivos | Narrativa secuencial del proceso, indicando los controles existentes y su referencia. La descripción secuencial, debe contener como mínimo los siguientes pasos:

Como se inicia el subproceso documentado
Autorizaciones
Registro de las transacciones
Aplicación utilizada (Software)
Cuál es fin de proceso y con que subproceso se conecta
    Enfoque | Se describen no tan solo los controles administrativos implementados, sino que también se deben describir los “controles sistémicos relevantes”.

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 15 ---
Contenido de Texto:
  A  Focus on Control Activities
  Matriz de Controles
  Documentación del control interno
  Control clave: Son los controles internos necesarios para prevenir o detectar errores u omisiones importantes en relación a las aserciones financieras, para los procesos y cuentas significativas.,

Tablas:
  Tabla 1:
    Objetivos | Descripción precisa del control considerado “clave”, la cual además señala los documentos, reportes e instancias de aprobación existentes, documentadas y verificables por un tercero.
    Responsables | “Dueños” del proceso
    Comentarios | Deben señalarse los documentos/reportes que permitan posteriormente, probar la existencia y funcionamiento del control por parte de un tercero.

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 16 ---
Contenido de Texto:
  Matriz de Controles
  Documentación del control interno

Tablas:
  Tabla 1:
    Formato | Es una matriz generalmente realizada en Excel, que identifica para el proceso considerado relevante, los siguientes contenidos:

Nombre del Proceso o subproceso.
Riesgo identificado.
Numero de referencia del control.
Descripción de todos los controles: Detalla si es una revisión analítica, conciliaciones, procedimientos de revisión, acceso a sistemas, control de configuración de sistemas, segregación de funciones
Aserciones de los estados financieros cubiertas por cada control (Existencia; Integridad; Derechos y Obligaciones; Valuación y Presentación – exposición).
Tipo de control: Preventivo o Detectivo / Monitoreo o transaccional / Automatizado o Manual.
Tipo de documento en que se materializa el control
Ejecutado por: Persona dueña del control. 
Sistema / Aplicación que soporta el control.

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 17 ---
Contenido de Texto:
  Seguridad Informática y Principios de AuditoríaRelevamiento y evaluación de diseño de Controles
  Profesor Pablo Gil

Imágenes:
  Imagen detectada: Picture 10

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 18 ---
Contenido de Texto:
  18
  Relevamiento y evaluación de diseño de Controles

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 19 ---
Contenido de Texto:
  ¿Qué es un walkthrough?Técnica que implica realizar un seguimiento de una transacción desde que se origina hasta que finaliza y se registra, si corresponde, en la contabilidad. El procesamiento de la transacción es analizado a través de los procesos y sistemas de la compañía.
  19
  Relevamiento y evaluación de diseño de Controles

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 20 ---
Contenido de Texto:
  ¿Para que se utilizan los walkthrough?
  20
  Realizar el entendimiento del proceso de punta a punta, evaluar el diseño de los controles y determinar si los controles se encuentran implementados (un caso).
Entender el flujo de transacciones relacionadas a las aserciones / objetivos de procesamiento de la información importantes o a las operaciones relevantes que se deben cubrir de acuerdo al alcance definido.
Para la ejecución del WT, se utiliza una combinación de las técnicas de indagación, observación, inspección y re-ejecución.
  Relevamiento y evaluación de diseño de Controles

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 21 ---
Contenido de Texto:
  Se realizan preguntas con el objetivo de:
Entender el proceso, incluyendo distintos tipos de transacciones
Identificar puntos en el proceso donde no hay un control o el control no esta diseñado de manera efectiva
Indagar con la persona que realiza el control sobre cómo funciona el control, la naturaleza de los errores detectados y el proceso de corrección de dichos errores.
  ¿Para que se utilizan los walkthrough? (cont.)
  21
  Relevamiento y evaluación de diseño de Controles

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 22 ---
Contenido de Texto:
  22
  Indagar/entrevistar a todas las personas que realizan los controles y no solo al responsable del proceso.
Corroborar lo relevado realizando procedimientos adicionales.
Seguir una transacción desde su inicio a registración y utilizando la misma documentación que retiene la persona que ejecuta el control. 
Realizar consultas relacionadas a como se mitigan y previenen los riesgos de fraude.
  Otros aspectos a tener en cuenta
  Relevamiento y evaluación de diseño de Controles

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 23 ---
Contenido de Texto:
  Confirmar el entendimiento del proceso
Identificar riesgos
Evaluar el diseño y la implementación de los controles
Sistemas que soportan el proceso
Reportes & planillas de calculo relevantes 
Identificación de controles de acceso (Accesos críticos y SoD) 
Impacto contable de la transacción analizada
  23
  Salidas
  Relevamiento y evaluación de diseño de Controles

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 24 ---
Contenido de Texto:
  Seguridad Informática y Principios de AuditoríaPruebas de controles
Informes
  Profesor Pablo Gil

Imágenes:
  Imagen detectada: Picture 10

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 25 ---
Contenido de Texto:
  Luego de evaluar si el diseño de los controles es adecuado, es necesario validar que los controles han operado de forma efectiva durante todo el período sujeto a revisión.Es por ello que se realizan pruebas de controles. Normalmente los controles que se prueban son los controles claves, que son aquellos que de manera más eficiente generan “aseguramiento” sobre los objetivos de procesamiento de la información o aserciones que el auditor busca validar.
  Prueba/validación de controles

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 26 ---
Contenido de Texto:
  Validación de controles

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 27 ---
Contenido de Texto:
  1- Desarrollar el plan de pruebas
En base al riesgo asociado al control y al objetivo que el control intenta cubrir, seleccionar la naturaleza, oportunidad y alcance adecuados para probar el control
2- Ejecutar la prueba
Llevar a cabo el plan de pruebas diseñado. En algunos casos será necesario obtener previamente el universo total de transacciones asociadas al control para poder seleccionar una muestra en base a los criterios definidos.
  ¿Cuáles son los pasos a seguir a la hora de realizar pruebas de controles?
  Validación de controles

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 28 ---
Contenido de Texto:
  3- Concluir sobre la efectividad del control
Existe evidencia suficiente?
El control operó durante todo el período?
El control operó de la manera en que estaba diseñado?
4- Documentar los procedimientos realizados y las conclusiones
  ¿Cuáles son los pasos a seguir a la hora de realizar pruebas de controles?
  Validación de controles

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 29 ---
Contenido de Texto:
  Tamaño de muestras
  Controles manuales o dependientes de IT
  Controles automáticos
  N casos dependiendo de la frecuencia de operación del control (ej diaria, semanal, mensual, etc). También se deben validar las fuentes de información utilizadas para ejecutar el control
  Normalmente, si operan correctamente 1 vez, deberían operar siempre de la misma manera (siempre y cuando los CGTI que lo soporten operen de manera adecuada y no cambien los programas asociados al control automático)
  Validación de controles

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 30 ---
Contenido de Texto:
  Accesos
  Entender cómo se restringe el acceso a funciones del sistema
  Comprender configuración
  2.	Analizar quién posee acceso a las funciones que se están evaluando
  Identificar quién posee acceso
  3.	Obtener evidencia sobre si el acceso analizado corresponde a las personas adecuadas
  Evaluar si el acceso es adecuado
  Validación de controles

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 31 ---
Contenido de Texto:
  Segregación de funciones
  Entender de qué forma opera el proceso evaluado
  Comprender el proceso de negocio
  2.  Basado en el entendimiento anterior, identificar cuáles son los casos potenciales de segregación de funciones
  Identificar conflictos potenciales
  3.	Para los conflictos identificados, comprender si la gerencia los mitiga a través de restricción de accesos en los sistemas o a través de controles compensatorios
  Identificar controles
  4.	Realizar las pruebas de los aspectos identificados en el punto 3)
  Probar los controles
  Validación de controles

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 32 ---
Contenido de Texto:
  Interfaces
  Pueden ser probadas de dos maneras:

A) Probar el funcionamiento automático de la interfase
Validar la lógica de construcción entre la fuente y el destino
Validar la operación de la transferencia de datos (la interfase se ejecutó? Si hubo errores, cómo se corrigieron?)

B) Probar la reconciliación de datos como evidencia de la operación de la interfase (control de negocio)
  Validación de controles

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 33 ---
Contenido de Texto:
  Informes
  Resumen ejecutivo
Objetivo
Alcance
Hallazgos y oportunidades de mejora
Descripción de la situación observada
Descripción del efecto / riesgo
Recomendación
Comentarios de la gerencia

--------------------------------------------------
