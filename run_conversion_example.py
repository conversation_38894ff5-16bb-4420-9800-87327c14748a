#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script de ejemplo para ejecutar la conversión PPTX a PDF
Demuestra diferentes formas de usar el conversor
"""

import sys
import os
from pathlib import Path

# Agregar el directorio actual al path para importar el conversor
sys.path.append(str(Path(__file__).parent))

from pptx_to_pdf_converter import PPTXToPDFConverter

def example_basic_conversion():
    """Ejemplo básico de conversión usando las rutas por defecto"""
    print("🔄 Ejecutando conversión básica...")
    print("=" * 50)
    
    try:
        # Crear conversor con rutas por defecto
        converter = PPTXToPDFConverter(
            source_dir="Presentaciones/PPTX a convertir a PDF",
            dest_dir="Presentaciones/PDF convertidos para validacion manual"
        )
        
        # Ejecutar conversión
        stats = converter.convert_all_files()
        
        # Mostrar reporte
        converter.generate_report()
        
        return stats['failed'] == 0
        
    except Exception as e:
        print(f"❌ Error en conversión básica: {e}")
        return False

def example_custom_paths():
    """Ejemplo con rutas personalizadas"""
    print("\n🔄 Ejemplo con rutas personalizadas...")
    print("=" * 50)
    
    # Rutas personalizadas (puedes cambiarlas según necesites)
    custom_source = "Presentaciones/PPTX a convertir a PDF"
    custom_dest = "Presentaciones/PDF_Personalizados"
    
    try:
        converter = PPTXToPDFConverter(
            source_dir=custom_source,
            dest_dir=custom_dest,
            log_level="DEBUG"  # Más detalle en los logs
        )
        
        stats = converter.convert_all_files()
        converter.generate_report()
        
        return stats['failed'] == 0
        
    except Exception as e:
        print(f"❌ Error en conversión personalizada: {e}")
        return False

def check_prerequisites():
    """Verifica que todo esté listo para la conversión"""
    print("🔍 Verificando prerequisitos...")
    
    # Verificar que exista el directorio origen
    source_dir = Path("Presentaciones/PPTX a convertir a PDF")
    if not source_dir.exists():
        print(f"❌ Directorio origen no encontrado: {source_dir}")
        return False
    
    # Verificar que haya archivos .pptx
    pptx_files = list(source_dir.glob("*.pptx"))
    if not pptx_files:
        print(f"⚠️  No se encontraron archivos .pptx en: {source_dir}")
        print("   Coloca algunos archivos .pptx en el directorio para probar la conversión")
        return False
    
    print(f"✅ Encontrados {len(pptx_files)} archivos .pptx listos para conversión")
    
    # Verificar dependencias
    try:
        import comtypes.client
        print("✅ Biblioteca comtypes disponible")
    except ImportError:
        print("❌ Biblioteca comtypes no encontrada. Ejecuta: pip install comtypes")
        return False
    
    try:
        from tqdm import tqdm
        print("✅ Biblioteca tqdm disponible")
    except ImportError:
        print("❌ Biblioteca tqdm no encontrada. Ejecuta: pip install tqdm")
        return False
    
    return True

def show_file_list():
    """Muestra la lista de archivos que serán convertidos"""
    print("\n📋 Archivos encontrados para conversión:")
    print("-" * 50)
    
    source_dir = Path("Presentaciones/PPTX a convertir a PDF")
    pptx_files = list(source_dir.glob("*.pptx"))
    
    if not pptx_files:
        print("   No se encontraron archivos .pptx")
        return
    
    for i, file_path in enumerate(pptx_files, 1):
        file_size = file_path.stat().st_size / 1024 / 1024  # MB
        print(f"   {i:2d}. {file_path.name} ({file_size:.1f} MB)")
    
    print(f"\nTotal: {len(pptx_files)} archivos")

def main():
    """Función principal del ejemplo"""
    print("🎯 DEMOSTRACIÓN DEL CONVERSOR PPTX A PDF")
    print("=" * 60)
    
    # Verificar prerequisitos
    if not check_prerequisites():
        print("\n❌ No se pueden ejecutar los ejemplos debido a prerequisitos faltantes")
        print("💡 Ejecuta primero: python setup_converter.py")
        return
    
    # Mostrar archivos disponibles
    show_file_list()
    
    # Preguntar al usuario qué quiere hacer
    print("\n🤔 ¿Qué te gustaría hacer?")
    print("1. Ejecutar conversión básica")
    print("2. Ejecutar conversión con rutas personalizadas")
    print("3. Solo mostrar información (sin convertir)")
    print("0. Salir")
    
    try:
        choice = input("\nSelecciona una opción (0-3): ").strip()
        
        if choice == "1":
            success = example_basic_conversion()
            if success:
                print("\n🎉 ¡Conversión completada exitosamente!")
            else:
                print("\n⚠️  La conversión tuvo algunos problemas")
                
        elif choice == "2":
            success = example_custom_paths()
            if success:
                print("\n🎉 ¡Conversión personalizada completada!")
            else:
                print("\n⚠️  La conversión personalizada tuvo problemas")
                
        elif choice == "3":
            print("\n📊 Información mostrada. No se realizaron conversiones.")
            
        elif choice == "0":
            print("\n👋 ¡Hasta luego!")
            
        else:
            print("\n❌ Opción no válida")
            
    except KeyboardInterrupt:
        print("\n\n👋 Operación cancelada por el usuario")
    except Exception as e:
        print(f"\n❌ Error inesperado: {e}")

if __name__ == "__main__":
    main()
