#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script de resumen de conversión PPTX a PDF
Muestra estadísticas y verifica la integridad de los archivos convertidos
"""

import os
from pathlib import Path
from datetime import datetime

def verificar_conversion():
    """Verifica el estado de la conversión PPTX a PDF"""
    print("📊 RESUMEN DE CONVERSIÓN PPTX A PDF")
    print("=" * 60)
    print(f"Fecha de verificación: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Directorios
    source_dir = Path("Presentaciones/PPTX a convertir a PDF")
    dest_dir = Path("Presentaciones/PDF convertidos para validacion manual")
    
    # Verificar directorios
    if not source_dir.exists():
        print(f"❌ Directorio origen no encontrado: {source_dir}")
        return
    
    if not dest_dir.exists():
        print(f"❌ Directorio destino no encontrado: {dest_dir}")
        return
    
    # Buscar archivos PPTX
    pptx_files = list(source_dir.glob("*.pptx"))
    pptx_files = [f for f in pptx_files if not f.name.startswith("~$")]
    
    # Buscar archivos PDF
    pdf_files = list(dest_dir.glob("*.pdf"))
    
    print(f"📁 Directorio origen: {source_dir}")
    print(f"📁 Directorio destino: {dest_dir}")
    print()
    
    print(f"📋 ESTADÍSTICAS:")
    print(f"   Archivos PPTX encontrados: {len(pptx_files)}")
    print(f"   Archivos PDF generados: {len(pdf_files)}")
    
    if len(pptx_files) == len(pdf_files):
        print(f"   ✅ Estado: CONVERSIÓN COMPLETA")
        tasa_exito = 100.0
    else:
        print(f"   ⚠️  Estado: CONVERSIÓN PARCIAL")
        tasa_exito = (len(pdf_files) / len(pptx_files)) * 100 if pptx_files else 0
    
    print(f"   📈 Tasa de éxito: {tasa_exito:.1f}%")
    print()
    
    # Verificar correspondencia archivo por archivo
    print("🔍 VERIFICACIÓN DETALLADA:")
    print("-" * 40)
    
    archivos_convertidos = []
    archivos_faltantes = []
    
    for pptx_file in pptx_files:
        # Generar nombre esperado del PDF
        pdf_name = pptx_file.stem + ".pdf"
        pdf_path = dest_dir / pdf_name
        
        if pdf_path.exists():
            # Verificar tamaño del archivo
            pdf_size = pdf_path.stat().st_size
            pptx_size = pptx_file.stat().st_size
            
            print(f"✅ {pptx_file.name}")
            print(f"   → {pdf_name} ({pdf_size / 1024 / 1024:.1f} MB)")
            archivos_convertidos.append((pptx_file.name, pdf_name))
        else:
            print(f"❌ {pptx_file.name}")
            print(f"   → PDF no encontrado: {pdf_name}")
            archivos_faltantes.append(pptx_file.name)
    
    print()
    
    # Resumen final
    if archivos_faltantes:
        print(f"⚠️  ARCHIVOS PENDIENTES DE CONVERSIÓN ({len(archivos_faltantes)}):")
        for archivo in archivos_faltantes:
            print(f"   • {archivo}")
        print()
        print("💡 Para convertir archivos faltantes, ejecuta:")
        print("   python pptx_to_pdf_converter_simple.py")
    else:
        print("🎉 ¡TODOS LOS ARCHIVOS HAN SIDO CONVERTIDOS EXITOSAMENTE!")
        print()
        print("📂 Los archivos PDF están disponibles en:")
        print(f"   {dest_dir.absolute()}")
    
    print()
    
    # Información adicional
    print("📋 ARCHIVOS DISPONIBLES PARA VALIDACIÓN:")
    print("-" * 40)
    
    for pdf_file in sorted(pdf_files):
        size_mb = pdf_file.stat().st_size / 1024 / 1024
        mod_time = datetime.fromtimestamp(pdf_file.stat().st_mtime)
        print(f"📄 {pdf_file.name}")
        print(f"   Tamaño: {size_mb:.1f} MB | Modificado: {mod_time.strftime('%Y-%m-%d %H:%M')}")
    
    print()
    print("=" * 60)
    
    # Verificar logs
    log_dir = Path("logs")
    if log_dir.exists():
        log_files = list(log_dir.glob("pptx_conversion_*.log"))
        if log_files:
            latest_log = max(log_files, key=lambda x: x.stat().st_mtime)
            print(f"📋 Log más reciente: {latest_log.name}")
            print(f"   Ubicación: {latest_log.absolute()}")

def mostrar_ayuda():
    """Muestra información de ayuda sobre los scripts disponibles"""
    print("\n💡 SCRIPTS DISPONIBLES:")
    print("-" * 30)
    print("1. pptx_to_pdf_converter_simple.py  (RECOMENDADO)")
    print("   • Método más compatible y estable")
    print("   • Funciona con la mayoría de versiones de PowerPoint")
    print("   • Usa SaveAs con formato PDF")
    print()
    print("2. pptx_to_pdf_converter_win32.py")
    print("   • Usa win32com con ExportAsFixedFormat")
    print("   • Puede fallar en versiones antiguas de PowerPoint")
    print()
    print("3. pptx_to_pdf_converter.py")
    print("   • Usa comtypes")
    print("   • Puede tener problemas de visibilidad de ventana")
    print()
    print("4. setup_converter.py")
    print("   • Instala dependencias y verifica el sistema")
    print()
    print("5. run_conversion_example.py")
    print("   • Ejemplos interactivos de uso")

def main():
    """Función principal"""
    print("🔧 HERRAMIENTA DE VERIFICACIÓN DE CONVERSIÓN PPTX A PDF")
    print("Desarrollado para UBA FCE - Auditoría y Seguridad Informática")
    print()
    
    try:
        verificar_conversion()
        mostrar_ayuda()
        
    except Exception as e:
        print(f"❌ Error durante la verificación: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
