================================================================================
PRESENTACIÓN: UBA FCE - Seg Inf y Pprios Aud - NIST.pdf
================================================================================
Formato: .pdf
Total Páginas/Diapositivas: 22
Tamaño del archivo: 1,390,817 bytes
Fecha de extracción: 2025-06-14 21:40:31
Archivo original modificado: 2025-06-14 20:55:14

--- PÁGINA/DIAPOSITIVA 1 ---
Contenido de Texto:
  Seguridad Informática y
Principios de Auditoría
NIST
Profesor Pablo Gil

Imágenes:
  Imágenes detectadas: 1

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 2 ---
Contenido de Texto:
  NIST - National Institute of Standards and
Technology
● Es una agencia de la Administración de Tecnología del Departamento
de Comercio de los Estados Unidos.
● Objetivo: promover la innovación y la competencia industrial en
Estados Unidos mediante avances en metrología, normas y tecnología
de forma que mejoren la estabilidad económica y la calidad de vida.
(Ej: Containers)
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 2

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 3 ---
Contenido de Texto:
  ¿Por qué es relevante el Cybersecurity Framework del
NIST?
“El gran diferencial que ha presentado el CSF (cybersecurity framework)
respecto a sus antecesores es su simplicidad y flexibilidad;
simplicidad para poder transmitir una estrategia técnica en términos que
el negocio comprenda y flexibilidad para adecuarse a cualquier
organización.”
“Proporciona un enfoque prioritario, flexible, repetible, basado en el
rendimiento y rentabilidad”
Ciberseguridad: se refiere a un conjunto de técnicas y
mecanismos que se utilizan para proteger la integridad,
disponibilidad y confidencialidad de redes, sistemas,
aplicaciones y datos.
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 3

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 4 ---
Contenido de Texto:
  CSF consta de tres componentes:
• Framework Core
El Core es un conjunto de actividades y resultados de ciberseguridad deseados, organizados
en Categorías y alineados con Referencias Informativas a estándares aceptados por la
industria.
Consta de tres partes: Funciones, Categorías y Subcategorías.
• Niveles de implementación (Tiers)
Los niveles describen el grado en que las prácticas de gestión de riesgos de ciberseguridad de
una organización exhiben las características definidos en el Marco.
• Perfiles
Son la alineación única de una organización de sus requisitos y objetivos organizacionales, la
tolerancia al riesgo y los recursos con respecto a los resultados deseados del Framework Core
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 4

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 5 ---
Contenido de Texto:
  Nuevo Cybersecurity Framework
UBA FCE –Seguridad Informática y Principios de Auditoría

Imágenes:
  Imágenes detectadas: 1

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 6 ---
Contenido de Texto:
  https://blog.segu-info.com.ar/2023/08/nist-cybersecurity-framework-csf-v20.html
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 6

Imágenes:
  Imágenes detectadas: 2

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 7 ---
Contenido de Texto:
  Gobierno (GV) - ¿Como tomar y ejecutar decisiones
internas para GESI?
GV.OC Contexto Organizacional
GV.RM Estrategias de gestión de
riesgo
GV.RR Roles y responsabilidades
GV.PO Politicas y procedimientos
Es una función transversal y proporciona resultados para informar como una compañía lograra y
prioriza los resultados de la implementación de Framework, alineados con sus objetivos.
GOBIERNO dirige una comprensión de 198 contexto organizacional; el establecimiento de la
estrategia de ciberseguridad, gestión de riesgos de la cadena de suministro; funciones,
responsabilidades y autoridades; políticas, procesos y trámites.
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 7

Imágenes:
  Imágenes detectadas: 1

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 8 ---
Contenido de Texto:
  Identificar (ID) - ¿Que hay que proteger?
ID.AM Gestion de Activos
ID.BE Entorno Empresarial
ID.GV Gobierno
ID.RA Evaluación de Riesgo
ID.RM Estrategia de gestión de
Riesgo
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 8

Imágenes:
  Imágenes detectadas: 1

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 9 ---
Contenido de Texto:
  Proteger (PR) - ¿Con que lo protegeremos?
PR.AC Control de Acceso
PR.AT Concientizacion y
capacitacion
PR.DS Seguridad de los datos
PR.IP Procesos y procedimientos de
la protección de la información
PR.MA Manteniemitnos
PR.PT Tecnologia de proteccion
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 9

Imágenes:
  Imágenes detectadas: 1

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 10 ---
Contenido de Texto:
  Detectar (DE) - ¿Que usaremos para detección?
DE.AE Anomalias y Eventos
DE.CM Monitoreo continuo de
seguridad
DE.DP Proceso de detección
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 10

Imágenes:
  Imágenes detectadas: 1

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 11 ---
Contenido de Texto:
  Responder (RS) - ¿Como voy a contenerlo?
RS.RP Planificación de Respuesta
RS.CO Comunicacion
RS.AN Analisis
RS.MI Mitigación
RS.IM Mejora
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 11

Imágenes:
  Imágenes detectadas: 1

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 12 ---
Contenido de Texto:
  Recuperar (RC) - ¿Como voy a restaurar mis
capacidades?
RC.RP Planificación de recuperación
RC.IM Mejoras
RC.CO Comunicaciones
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 12

Imágenes:
  Imágenes detectadas: 1

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 13 ---
Contenido de Texto:
  CSF
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 13

Imágenes:
  Imágenes detectadas: 1

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 14 ---
Contenido de Texto:
  Niveles de implementación
Proporcionan un contexto sobre cómo una organización considera el riesgo de seguridad
cibernética y los procesos establecidos para gestionar dicho riesgo.
Los Niveles Parcial (Nivel 1) a Adaptable (Nivel 4) describen un grado cada vez mayor de
rigor y sofisticación en las prácticas de gestión de riesgos de seguridad cibernética. Ayudan a
determinar en qué medida la gestión del riesgo de seguridad cibernética se basa en las
necesidades empresariales y se integra a las prácticas generales de gestión del riesgo de una
organización.
El proceso de selección de Niveles considera las prácticas actuales de gestión de riesgos, el
entorno de amenazas, los requisitos legales y reglamentarios, las prácticas de intercambio de
información, los objetivos empresariales o de misión, los requisitos de seguridad cibernética
de la cadena de suministro y las limitaciones organizativas.
NO PRESENTAN NIVEL DE MADUREZ. Están destinados a respaldar la toma de decisiones
organizacionales sobre cómo gestionar el riesgo de seguridad cibernética, así como qué
dimensiones de la organización son de mayor prioridad y podrían recibir recursos
adicionales.
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 14

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 15 ---
Contenido de Texto:
  Niveles de implementación
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 15

Imágenes:
  Imágenes detectadas: 1

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 16 ---
Contenido de Texto:
  Niveles de implementación
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 16

Imágenes:
  Imágenes detectadas: 1

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 17 ---
Contenido de Texto:
  Cómo usarlo?
Para crear un programa de Ciberseguridad o mejorar un programa
existente:
1) Priorizar el alcance
2) Orientar
3) Crea un perfil actual
4) Realizar una evaluación de riesgo
5) Crea un perfil de destino
6) Determinar, analizar y priorizar brecha
7) Implementar un plan de acción
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 17

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 18 ---
Contenido de Texto:
  Centro de recursos del NIST
https://csrc.nist.gov/publications/sp800
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 18

Imágenes:
  Imágenes detectadas: 1

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 19 ---
Contenido de Texto:
  AWS FedRAMP SSP basada en NIST 800-53
● Responsabilidad compartida: usted se encargará de la seguridad y de los
parámetros de configuración de sus componentes de software, mientras
que AWS se encargará de la seguridad de la infraestructura.
● Responsabilidad exclusiva de los clientes: usted es el único responsable de
los sistemas operativos invitados, de las aplicaciones implementadas y de
una selección de recursos de red (por ejemplo, los firewalls).
Concretamente, usted es el único responsable de configurar y administrar
la seguridad en la nube.
● Responsabilidad exclusiva de AWS: AWS administra la infraestructura de
la nube, incluida la red, el almacenamiento de datos, los recursos del
sistema, los centros de datos, la seguridad física, la fiabilidad y el hardware
y software auxiliares. Las aplicaciones creadas en un sistema de AWS
heredan las características y las opciones configurables que AWS
proporciona. AWS es el único responsable de configurar y administrar la
seguridad de la nube.
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 19

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 20 ---
Contenido de Texto:
  Resumiendo
Los objetivos del marco de trabajo en su implementación en una organización se
podrían catalogar en los siguientes puntos:
● Describir la postura actual de ciberseguridad
● Describir el estado objetivo de ciberseguridad
● Identificar y priorizar oportunidades de mejora en el contexto de un
proceso continuo y repetible
● Evaluar el progreso hacia el estado objetivo
● Comunicación entre las partes interesadas internas y externas sobre el
riesgo de ciberseguridad
Todo esto enmarcado en un enfoque orientado a la gestión del riesgo.
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 20

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 21 ---
Contenido de Texto:
  NIST y Privacidad
La privacidad y la ciberseguridad tienen una fuerte conexión. Las actividades de
ciberseguridad de una organización también pueden crear riesgos para la
privacidad y las libertades civiles cuando se usa, recopila, procesa, mantiene o
divulga información personal.
Algunos ejemplos incluyen:
1. Recopilación o retención excesiva de información personal
2. Divulgación o uso de información personal no relacionada con actividades de
ciberseguridad
3. Actividades de mitigación de ciberseguridad que dan lugar a la denegación de
servicios u otros impactos potencialmente adversos similares, incluidos
algunos tipos de detección o monitoreo de incidentes que pueden inhibir la
libertad de expresión o asociación
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 21

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 22 ---
Contenido de Texto:
  Links
https://www.oas.org/es/sms/cicte/docs/OEA-AWS-Marco-NIST-de-Ciberseguridad-ESP.pdf
https://www.ftc.gov/es/system/files/attachments/understanding-nist-cybersecurity-
framework/cybersecurity_sb_nist-cyber-framework-es.pdf
https://www.esan.edu.pe/conexion/actualidad/2019/04/30/que-es-el-cybersecurity-framework-de-nist-de-los-
estados-unidos/
https://seguridadpersonalyprofesional.com/2019/04/09/las-cinco-funciones-incluidas-en-el-framework-de-nist/
https://www.nist.gov/cyberframework/framework
https://usuaria.org.ar/archivos/segurinfo2018/paraguay/presentaciones/SegurinfoParaguay2018-4.pdf
https://www.deacosta.com/guia-rapida-para-entender-el-marco-de-trabajo-de-ciberseguridad-del-nist/
https://es.pcisecuritystandards.org/_onelink_/pcisecurity/en2es/minisite/en/docs/PCI_DSS_v3.pdf
https://ciberseguridad.blog/como-implantar-el-framework-nist/
https://ciberseguridadenlinea.com/blog/Nueva-version-2-0-del-NIST-cybersecurity-framework-
CSF?expand_article=1
UBA FCE –Seguridad Informática y Principios de Auditoría Slide 22

--------------------------------------------------
