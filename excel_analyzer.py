#!/usr/bin/env python3
"""
Utilidad para analizar el cronograma del curso desde archivo Excel
"""

import pandas as pd
import sys
from datetime import datetime
import os

def analyze_excel_file(file_path):
    """
    Analiza el archivo Excel del cronograma del curso
    """
    try:
        # Leer el archivo Excel
        print(f"Analizando archivo: {file_path}")

        # Leer la hoja principal
        df = pd.read_excel(file_path, sheet_name='1ro 2025')
        print(f"Dimensiones: {df.shape[0]} filas x {df.shape[1]} columnas")

        # Mostrar toda la información del cronograma
        print("\n" + "="*80)
        print("CRONOGRAMA COMPLETO DEL CURSO")
        print("="*80)

        # Limpiar y mostrar el cronograma
        for index, row in df.iterrows():
            if pd.notna(row.iloc[0]) and str(row.iloc[0]).strip():
                clase = str(row.iloc[0]).strip()
                mes = str(row.iloc[1]).strip() if pd.notna(row.iloc[1]) else ""
                dia_semana = str(row.iloc[2]).strip() if pd.notna(row.iloc[2]) else ""
                dia = str(row.iloc[3]).strip() if pd.notna(row.iloc[3]) else ""
                contenido = str(row.iloc[4]).strip() if pd.notna(row.iloc[4]) else ""

                # Formatear la salida
                if clase.isdigit():
                    print(f"\nClase {clase}: {dia_semana} {dia} de {mes}")
                    if contenido and contenido != "nan":
                        print(f"  Contenido: {contenido}")
                elif any(keyword in clase.lower() for keyword in ['parcial', 'examen', 'final', 'recuperatorio']):
                    print(f"\n*** {clase.upper()} ***")
                    if mes and mes != "nan":
                        print(f"  Fecha: {dia_semana} {dia} de {mes}")
                elif clase not in ['Universidad de Buenos Aires - FCE', 'Materia:', 'Curso:', 'Profesor', 'Período', 'Clase']:
                    print(f"\n{clase}")
                    if contenido and contenido != "nan":
                        print(f"  {contenido}")

        # Extraer información específica de exámenes
        print("\n" + "="*80)
        print("INFORMACIÓN DE EXÁMENES")
        print("="*80)

        exam_info = {}
        for index, row in df.iterrows():
            contenido = str(row.iloc[4]).strip() if pd.notna(row.iloc[4]) else ""
            if any(keyword in contenido.lower() for keyword in ['primer examen parcial', 'segundo examen parcial']):
                mes = str(row.iloc[1]).strip() if pd.notna(row.iloc[1]) else ""
                dia_semana = str(row.iloc[2]).strip() if pd.notna(row.iloc[2]) else ""
                dia = str(row.iloc[3]).strip() if pd.notna(row.iloc[3]) else ""

                if 'primer' in contenido.lower():
                    exam_info['Parcial 1'] = {
                        'fecha': f"{dia_semana} {dia} de {mes}",
                        'fila': index
                    }
                elif 'segundo' in contenido.lower():
                    exam_info['Parcial 2'] = {
                        'fecha': f"{dia_semana} {dia} de {mes}",
                        'fila': index
                    }

        for exam, info in exam_info.items():
            print(f"{exam}: {info['fecha']} (fila {info['fila']})")

        # Determinar qué clases van antes de cada parcial
        print("\n" + "="*80)
        print("CLASIFICACIÓN DE CONTENIDO POR PARCIAL")
        print("="*80)

        parcial1_row = exam_info.get('Parcial 1', {}).get('fila', 999)
        parcial2_row = exam_info.get('Parcial 2', {}).get('fila', 999)

        parcial1_content = []
        parcial2_content = []

        for index, row in df.iterrows():
            clase = str(row.iloc[0]).strip() if pd.notna(row.iloc[0]) else ""
            contenido = str(row.iloc[4]).strip() if pd.notna(row.iloc[4]) else ""

            if clase.isdigit() and contenido and contenido != "nan":
                if index < parcial1_row:
                    parcial1_content.append(contenido)
                elif index < parcial2_row:
                    parcial2_content.append(contenido)

        print("\nContenido para Parcial 1:")
        for i, content in enumerate(parcial1_content, 1):
            print(f"  {i}. {content}")

        print("\nContenido para Parcial 2:")
        for i, content in enumerate(parcial2_content, 1):
            print(f"  {i}. {content}")

        return exam_info, parcial1_content, parcial2_content

    except Exception as e:
        print(f"Error al analizar el archivo: {e}")
        return None, [], []

def main():
    file_path = "Cronograma_del_Curso.xlsx"

    if not os.path.exists(file_path):
        print(f"Error: No se encontró el archivo {file_path}")
        return

    print("ANALIZADOR DE CRONOGRAMA DEL CURSO")
    print("=" * 50)

    exam_info, parcial1_content, parcial2_content = analyze_excel_file(file_path)

    if exam_info is not None:
        print("\nAnálisis completado exitosamente.")
        return exam_info, parcial1_content, parcial2_content
    else:
        print("\nEl análisis falló.")
        return None, [], []

if __name__ == "__main__":
    main()
