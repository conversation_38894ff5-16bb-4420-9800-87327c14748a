================================================================================
PRESENTACIÓN: UBA FCE - Seg Inf y Ppios Aud - ITGC.pptx
================================================================================
Formato: .pptx
Total Páginas/Diapositivas: 31
Tamaño del archivo: 394,456 bytes
Fecha de extracción: 2025-06-14 21:40:29
Archivo original modificado: 2025-06-14 20:55:59

--- PÁGINA/DIAPOSITIVA 1 ---
Contenido de Texto:
  Seguridad Informática y Principios de Auditoría Controles Generales de TI (ITGC)
  Profesor <PERSON>s:
  Imagen detectada: Picture 3

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 2 ---
Contenido de Texto:
  Controles Generales de TI (CGTI)
  Slide 2
  EEFF
  Controles a nivel entidad (indirectos)
  Segregación de funciones

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 3 ---
Contenido de Texto:
  Controles Generales de TI (CGTI)
  Slide 3
  Controles a nivel entidad (indirectos)
  Políticas, procedimientos y actividades de control que se relacionan con varios sistemas/aplicaciones y soportan el funcionamiento efectivo de los controles a nivel de transacción.

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 4 ---
Contenido de Texto:
  Slide 4
  Controles Generales de TI (CGTI)
  Los CGTI son relevantes ya que constituyen el ambiente de generación y procesamiento de:


Cálculos realizados por las aplicaciones
Reportes generados por las aplicaciones
Controles automáticos
Seguridad lógica (incluyendo segregación de funciones)
Interfases entre aplicaciones

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 5 ---
Contenido de Texto:
  Slide 5
  Controles Generales de TI (CGTI)
  Riesgos que surgen del ambiente de TI (agrupados en 4 dominios)
  Acceso al software y a los datos
Accesos no autorizados a las aplicaciones por parte de usuarios de negocio
Accesos no autorizados a las aplicaciones por parte de usuarios de TI
Cambios no autorizados a los datos
  Cambios a los programas
Cambios no solicitados por el negocio
Cambios que introducen errores en las aplicaciones
Cambios directos y no autorizados a los programas
Cambios directos y no autorizados a la configuración del sistema
Actualizaciones inadecuadas de sistemas operativos y bases de datos
  Desarrollo de sistemas
Los sistemas implementados procesan datos de manera incorrecta debido a problemas de codificación o configuración
Errores al migrar registros de transacciones y/o datos maestros
  Operaciones computarizadas
Fallas en los sistemas que causan pérdida de datos o transacciones o incapacidad para acceder a ellos según se requiera
Intervención manual inapropiada o fallas en el procesamiento de trabajos programados

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 6 ---
Contenido de Texto:
  Dominio: Accesos a Programas y DatosRiesgos:Accesos no autorizados a las aplicaciones por parte de usuarios de negocioAccesos no autorizados a las aplicaciones por parte de usuarios de TICambios no autorizados a los datos

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 7 ---
Contenido de Texto:
  Slide 7
  Controles Generales de TI (CGTI)
  Acceso al software y a los datos
  Seguridad Cloud

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 8 ---
Contenido de Texto:
  Slide 8
  Controles Generales de TI (CGTI)
  La organización debe asegurar que:
Sólo los usuarios de negocio autorizados tienen acceso a las aplicaciones
Los niveles de acceso de los usuarios dentro de las aplicaciones son apropiados y no contradicen la segregación de funciones
Las cuentas privilegiadas (o superusuarios) dentro de las aplicaciones son controladas
  Datos de 
negocio
  Usuarios de
negocio
  Aplicación
  Acceso al software y a los datos – Seguridad de la aplicación
  Superusuarios

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 9 ---
Contenido de Texto:
  Slide 9
  Controles Generales de TI (CGTI)
  Controles Preventivos
Diseñar roles adecuados
Proveer un adecuado proceso de aprovisionamiento de usuarios
Aplicar controles compensatorios cuando el acceso no puede ser evitado
Restringir y controlar los accesos de contratistas/consultores
  Controles Detectivos
Analizar roles y responsabilidades de usuarios para conflictos SoD
Identicar y corregir conflictos SoD
Monitorear actividad de usuarios y/o funciones críticas
  Las empresas necesitan saber quién tiene acceso a cada función en las aplicaciones, y asegurarse de que no se otorguen privilegios inapropiados
  Acceso al software y a los datos – Seguridad de la aplicación

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 10 ---
Contenido de Texto:
  Slide 10
  Controles Generales de TI (CGTI)
  La seguridad de datos se refiere a:
Seguridad sobre bases de datos, archivos y/o conjuntos de datos
Controles sobre el acceso directo a los datos utilizando funciones especiales
  Acceso al software y a los datos – Seguridad de los datos
  Datos de 
negocio
  Usuarios de
negocio
  Aplicación
  Usuarios de
IT (DBA)

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 11 ---
Contenido de Texto:
  Slide 11
  Controles Generales de TI (CGTI)
  Acceso al software y a los datos – Seguridad de los datos
  ¿Cómo se controla?:

Revisando y autorizando debidamente las solicitudes de acceso a las bases de datos y archivos
Removiendo de forma oportuna los accesos del personal desvinculado
Monitoreando de forma periódica las transacciones y actividades de los superusuarios y/o usuarios administradores
Aplicando estándares robustos de seguridad y contraseñas adecuadas a las bases de datos y archivos

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 12 ---
Contenido de Texto:
  Slide 12
  Controles Generales de TI (CGTI)
  Acceso al software y a los datos – Seguridad del Sistema operativo
  Las aplicaciones, bases de datos y software de red residen en hardware/infraestructura

Cada servidor o instancia posee su propio sistema operativo, aunque la seguridad puede controlarse a través de funcionalidad común de seguridad (ej. controladores de dominio)

Los sistemas operativos poseen ciertas cuentas privilegiadas que pueden otorgar accesos amplios a aplicaciones y datos

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 13 ---
Contenido de Texto:
  Slide 13
  Controles Generales de TI (CGTI)
  Acceso al software y a los datos – Seguridad del Sistema operativo
  ¿Cómo se controla?:

Restringiendo el acceso a cuentas privilegiadas solamente al personal apropiado

Monitoreando de forma periódica la actividad de los superusuarios y/o usuarios administradores

Aplicando estándares robustos de seguridad y contraseñas adecuadas a las sistemas operativos

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 14 ---
Contenido de Texto:
  Slide 14
  Controles Generales de TI (CGTI)
  Acceso al software y a los datos – Administración de accesos de usuarios de negocio

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 15 ---
Contenido de Texto:
  Slide 15
  Controles Generales de TI (CGTI)
  Acceso al software y a los datos – Administración de accesos de usuarios técnicos
  Todas las aplicaciones, sistemas operativos y bases de datos (también otros componentes de TI tales como firewalls) poseen un “administrador” o cuentas privilegiadas:
Windows = Administrador de dominio
UNIX = Root
OS/400 = QSECOFR
Base de datos SQL Server = DBA
Base de datos Oracle = SYS, SYSTEM
Se requieren medidas de seguridad adicionales (por ejemplo ensobrado de claves – hoy con software) y asignación específica al personal que desempeña la función.

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 16 ---
Contenido de Texto:
  Dominio: Desarrollo y Cambios a los programasRiesgos:- Cambios no solicitados por el negocio- Cambios que introducen errores en las aplicaciones- Cambios directos y no autorizado a los programas- Cambios directos y no autorizados a la configuración del sistema- Actualizaciones inadecuadas de sistemas operativos y bases de datos- Los sistemas implementados procesan datos de manera incorrecta debido a problemas de codificación o configuración / desarrollos no autorizados- Errores al migrar registros de transacciones y/o datos maestros

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 17 ---
Contenido de Texto:
  Slide 17
  Ambientes de procesamiento – separación de entornos o ambientes
  Nota: la separación de ambientes puede ser física o lógica

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 18 ---
Contenido de Texto:
  Slide 18
  Desarrollo de sistemas
  Naturaleza del cambio
  Actualizaciones menores
  Arreglos
  Pequeñas mejoras
  Actualización de la aplicación
(versions antiguas o desactualizadas)
  Nueva implementación
(Sistema / Modulo)
  Cambios a programas
  Desarrollo de sistemas

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 19 ---
Contenido de Texto:
  Slide 19
  Desarrollo de sistemas
  Ciclo de vida tradicional del desarrollo de sistemas
  Segregación de funciones
  Administración de proyectos

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 20 ---
Contenido de Texto:
  Slide 20
  Metodologías ágiles
  Marco de trabajo - SCRUM
  Principales roles
Scrum Master: hace cumplir 
el marco de trabajo Scrum
Product Owner (negocio): 
representa a los stakeholders
Development Team: equipo
de desarrollo
  Principales documentos
Product Backlog: requisitos de 
alto nivel que definen el trabajo 
a realizar
Sprint Backlog: requisitos a 
desarrollar en el siguiente sprint
Definition of Done: documento
que determina que tarea realizada
  Flujo de Trabajo
Sprint Planning: evento de planificación al inicio del sprint
Daily Scrum: reunión diaria de status del proyecto
Sprint Review: al final el sprint se realiza una revisión y retrospectiva del desarrollo
Sprint Retro: al final el sprint se realiza reunión de mejora continua del proceso

Imágenes:
  Imagen detectada: Picture 1

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 21 ---
Contenido de Texto:
  Slide 21
  Cambios a los sistemas
  Controles Preventivos / Detectivos
Contar con una adecuada separación de ambientes (DEV-QA-PRD)
Contar con una adecuada segregación de funciones (personal de desarrollo no accede a producción)
Verificar que los desarrollos/cambios fueron debidamente solicitados y especificados (ej: doc. con diseño funcional del cambio)
Verificar que los desarrollos/cambios fueron probados en ambiente de prueba previo a su pasaje en producción (user acceptance testing)
Verificar que los desarrollos/cambios estén debidamente autorizados previo a su pasaje a producción
Verificar que las migraciones o conversiones de datos fueron debidamente probadas y documentadas previo a la puesta en producción de un nuevo sistema
  Las organizaciones necesitan contar con un proceso de desarrollo y/o cambios a los programas definido y formalizado

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 22 ---
Contenido de Texto:
  Dominio: Operaciones computarizadasRiesgos:- Fallas en los sistemas que causa pérdida de datos/transacciones o incapacidad para acceder a ellos según se requiera- Intervención manual inapropiada o fallas en el procesamiento de trabajos programados

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 23 ---
Contenido de Texto:
  Controles generales de TI
  Slide 23
  Operaciones computarizadas

¿Por que las operaciones computarizadas de TI son importantes?

Asegurar que los sistemas que soportan los procesos principales del negocio están funcionando de manera correcta y garantizan que los datos se almacenan, conservan y transfieren de manera íntegra y exacta.
Para hacer un seguimiento eficiente de las actividades de procesamiento de información, backups, gestión de problemas, entre otros.

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 24 ---
Contenido de Texto:
  Controles generales de TI
  Slide 24
  Operaciones computarizadas
  Procesamiento automático de información
  Actividades de procesamiento de transacciones
  Operaciones del centro de cómputos
  Administración 
de operaciones
  Administración y resolución de problemas
  Actividades tendientes a asegurar que los datos de los sistemas productivos son procesados de forma íntegra y exacta, de acuerdo a los objetivos de la organización, y que los problemas de procesamiento son identificados y resueltos oportunamente

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 25 ---
Contenido de Texto:
  Controles generales de TI
  Slide 25
  Operaciones computarizadas
  Actividades de procesamiento de transacciones
  Programación y monitoreo de procesos batch (lotes)
Procesamiento en tiempo real
Procesamiento de interfaces
Monitoreo del procesamiento de transacciones

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 26 ---
Contenido de Texto:
  Controles generales de TI
  Slide 26
  Operaciones computarizadas
  Administración y resolución de problemas
  Identificación de problemas
Monitoreo de la red
Funciones de mesa de ayuda (niveles 1 a 3)

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 27 ---
Contenido de Texto:
  Controles generales de TI
  Slide 27
  Operaciones computarizadas
  Operaciones del centro de cómputos
  Backups y recupero de información
Plan de recuperación ante desastres y/o plan de continuidad de negocio
Protección ambiental
Gestión de la capacidad

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 28 ---
Contenido de Texto:
  Controles generales de TI
  Slide 28
  Operaciones computarizadas

Puntos de foco de los controles
a) Controles gerenciales
Políticas normas y procedimientos relacionados con monitoreo de procesos
Definición de roles y responsabilidades – SoD y Accesos críticos
Controles de monitoreo

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 29 ---
Contenido de Texto:
  Controles generales de TI
  Slide 29
  Operaciones computarizadas

Puntos de foco de los controles
b) Procesamiento batch
Proceso de alta baja y modificación de procesos batch / jobs
Proceso de recuperación de cada job
Monitoreo de procesos
Controles de acceso sobre la herramienta de seguimiento de jobs.

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 30 ---
Contenido de Texto:
  Controles generales de TI
  Slide 30
  Operaciones computarizadas

Puntos de foco de los controles
c) Procesamiento en tiempo real
Configuración de procesamiento en tiempo real (transacciones inter módulo, middleware)
Cómo se capturan y qué alertas generan los errores en el procesamiento en tiempo real
Accesos a los cambios de configuración

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 31 ---
Contenido de Texto:
  Controles generales de TI
  Slide 31
  Operaciones computarizadas

Puntos de foco de los controles
d) Backups y administración de problemas
El contenido y la frecuencia de los backups versus objetivos del negocio
Cómo se asegura la compañía que los backups estarán disponibles en caso de una emergencia
Proceso de restore de información

--------------------------------------------------
