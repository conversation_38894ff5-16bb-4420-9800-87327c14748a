#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script de unificación de archivos PDF según cronograma del curso
Unifica todas las presentaciones PDF en el orden correcto según el cronograma académico
Autor: Generado para UBA FCE - Auditoría y Seguridad Informática
Fecha: 2025-06-15
"""

import os
import sys
import logging
import re
from pathlib import Path
from datetime import datetime
from typing import List, Tuple, Dict, Optional
import pandas as pd
from tqdm import tqdm
import argparse

# Importar bibliotecas para PDF
try:
    from pypdf import PdfReader, PdfWriter
    from pypdf.generic import Destination
    PDF_LIB = "pypdf"
except ImportError:
    try:
        from PyPDF2 import PdfReader, PdfWriter
        PDF_LIB = "PyPDF2"
    except ImportError:
        PDF_LIB = None

# Para generar páginas de índice
try:
    from reportlab.pdfgen import canvas
    from reportlab.lib.pagesizes import letter, A4
    from reportlab.lib.styles import getSampleStyleSheet
    from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer
    from reportlab.lib.units import inch
    REPORTLAB_AVAILABLE = True
except ImportError:
    REPORTLAB_AVAILABLE = False

class PDFUnifier:
    """Unificador de archivos PDF según cronograma académico"""
    
    def __init__(self, cronograma_path: str, pdf_dir: str, output_dir: str, log_level: str = "INFO"):
        """
        Inicializa el unificador de PDFs
        
        Args:
            cronograma_path: Ruta al archivo Excel del cronograma
            pdf_dir: Directorio con archivos PDF a unificar
            output_dir: Directorio destino para el PDF unificado
            log_level: Nivel de logging (DEBUG, INFO, WARNING, ERROR)
        """
        if PDF_LIB is None:
            raise ImportError("Se requiere pypdf o PyPDF2. Instala: pip install pypdf")
        
        self.cronograma_path = Path(cronograma_path)
        self.pdf_dir = Path(pdf_dir)
        self.output_dir = Path(output_dir)
        self.log_level = log_level
        
        # Estadísticas de unificación
        self.stats = {
            'total_pdfs_found': 0,
            'pdfs_unified': 0,
            'pdfs_missing': 0,
            'total_pages': 0
        }
        
        # Listas para tracking
        self.unified_pdfs: List[Tuple[str, str, int]] = []  # (nombre, archivo, páginas)
        self.missing_pdfs: List[str] = []
        self.pdf_mapping: Dict[str, Path] = {}
        
        # Configurar logging
        self._setup_logging()
        
        # Validar directorios y archivos
        self._validate_paths()
    
    def _setup_logging(self):
        """Configura el sistema de logging"""
        # Crear directorio de logs si no existe
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        # Nombre del archivo de log con timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        log_file = log_dir / f"pdf_unification_{timestamp}.log"
        
        # Configurar logging
        logging.basicConfig(
            level=getattr(logging, self.log_level.upper()),
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        
        self.logger = logging.getLogger(__name__)
        self.logger.info("Iniciando unificación de PDFs según cronograma")
        self.logger.info(f"Log guardado en: {log_file}")
        self.logger.info(f"Biblioteca PDF utilizada: {PDF_LIB}")
    
    def _validate_paths(self):
        """Valida que existan los archivos y directorios necesarios"""
        # Verificar cronograma
        if not self.cronograma_path.exists():
            raise FileNotFoundError(f"Cronograma no encontrado: {self.cronograma_path}")
        
        # Verificar directorio de PDFs
        if not self.pdf_dir.exists():
            raise FileNotFoundError(f"Directorio de PDFs no encontrado: {self.pdf_dir}")
        
        # Crear directorio de salida si no existe
        try:
            self.output_dir.mkdir(parents=True, exist_ok=True)
            self.logger.info(f"Directorio de salida preparado: {self.output_dir}")
        except Exception as e:
            raise PermissionError(f"No se puede crear directorio de salida: {e}")
    
    def read_cronograma(self) -> List[str]:
        """
        Lee el cronograma Excel y extrae el orden de las presentaciones
        
        Returns:
            Lista ordenada de nombres de presentaciones
        """
        self.logger.info(f"Leyendo cronograma desde: {self.cronograma_path}")
        
        try:
            # Leer archivo Excel
            df = pd.read_excel(self.cronograma_path, sheet_name=0)
            
            self.logger.debug(f"Columnas encontradas: {list(df.columns)}")
            self.logger.debug(f"Filas en cronograma: {len(df)}")
            
            # Buscar columnas que contengan información de presentaciones
            presentation_order = []
            
            # Buscar en todas las columnas texto que parezca nombre de presentación
            for _, row in df.iterrows():
                for col in df.columns:
                    cell_value = str(row[col]).strip()
                    
                    # Filtrar valores que parezcan nombres de presentaciones
                    if (len(cell_value) > 10 and 
                        any(keyword in cell_value.lower() for keyword in 
                            ['uba', 'fce', 'seg', 'inf', 'aud', 'auditoria', 'seguridad']) and
                        cell_value not in presentation_order):
                        presentation_order.append(cell_value)
            
            # Si no encontramos presentaciones, usar una lista predeterminada basada en los archivos
            if not presentation_order:
                self.logger.warning("No se encontraron presentaciones en el cronograma, usando orden alfabético")
                presentation_order = self._get_default_order()
            
            self.logger.info(f"Orden de presentaciones extraído: {len(presentation_order)} elementos")
            for i, pres in enumerate(presentation_order, 1):
                self.logger.debug(f"  {i}. {pres}")
            
            return presentation_order
            
        except Exception as e:
            self.logger.error(f"Error leyendo cronograma: {e}")
            self.logger.warning("Usando orden predeterminado")
            return self._get_default_order()
    
    def _get_default_order(self) -> List[str]:
        """
        Genera un orden predeterminado basado en los archivos PDF encontrados
        
        Returns:
            Lista ordenada de nombres de presentaciones
        """
        # Orden lógico basado en el contenido del curso
        default_order = [
            "Introduccion a los fundamentos de Seguridad de la Informacion",
            "Conceptos introductorios Controles, Riesgos y Auditoría",
            "COSO",
            "Gestión de riesgos ERM",
            "NIST",
            "ISO series 27000",
            "GESI",
            "ITGC_Gobierno TI",
            "ITGC",
            "Procesos de Negocio",
            "Normas Auditoría",
            "Planificacion_Ejecución_Pruebas de Auditoria",
            "OWASP top 10",
            "Tipo de amenazas y ataques. Técnicas de explotación",
            "Pruebas de Penetracion & Auditoria de codigo",
            "Seguridad en el SDLC y DevSecOps",
            "Seg SO y BD",
            "Awareness",
            "BCP",
            "SOC radar",
            "Detección y respuesta a incidentes de Seguridad"
        ]
        
        return default_order
    
    def find_pdf_files(self) -> Dict[str, Path]:
        """
        Busca archivos PDF en el directorio especificado
        
        Returns:
            Diccionario mapeando nombres simplificados a rutas de archivos
        """
        self.logger.info(f"Buscando archivos PDF en: {self.pdf_dir}")
        
        pdf_files = {}
        
        # Buscar archivos PDF directamente en el directorio (sin subdirectorios)
        for pdf_file in self.pdf_dir.glob("*.pdf"):
            # Extraer nombre simplificado del archivo
            simplified_name = self._simplify_filename(pdf_file.name)
            pdf_files[simplified_name] = pdf_file
            
            self.logger.debug(f"PDF encontrado: {pdf_file.name} -> {simplified_name}")
        
        self.stats['total_pdfs_found'] = len(pdf_files)
        self.logger.info(f"Total de archivos PDF encontrados: {len(pdf_files)}")
        
        return pdf_files
    
    def _simplify_filename(self, filename: str) -> str:
        """
        Simplifica el nombre del archivo para facilitar el mapeo
        
        Args:
            filename: Nombre completo del archivo
            
        Returns:
            Nombre simplificado
        """
        # Remover extensión
        name = filename.replace('.pdf', '').replace('.pptx.pdf', '')
        
        # Remover prefijos comunes
        name = re.sub(r'^UBA FCE - (Seg Inf y Ppios Aud - |Auditoria y Seguridad - )', '', name)
        
        # Limpiar caracteres especiales
        name = re.sub(r'[^\w\s&-]', '', name)
        
        return name.strip()
    
    def map_presentations_to_files(self, presentation_order: List[str], pdf_files: Dict[str, Path]) -> List[Tuple[str, Path]]:
        """
        Mapea las presentaciones del cronograma a archivos PDF
        
        Args:
            presentation_order: Lista ordenada de presentaciones
            pdf_files: Diccionario de archivos PDF encontrados
            
        Returns:
            Lista de tuplas (nombre_presentacion, ruta_archivo)
        """
        self.logger.info("Mapeando presentaciones a archivos PDF...")
        
        mapped_files = []
        
        for presentation in presentation_order:
            simplified_pres = self._simplify_filename(presentation)
            
            # Buscar coincidencia exacta
            if simplified_pres in pdf_files:
                mapped_files.append((presentation, pdf_files[simplified_pres]))
                self.logger.debug(f"✓ Mapeado: {presentation} -> {pdf_files[simplified_pres].name}")
                continue
            
            # Buscar coincidencia parcial
            best_match = None
            best_score = 0
            
            for pdf_name, pdf_path in pdf_files.items():
                # Calcular similitud simple
                common_words = set(simplified_pres.lower().split()) & set(pdf_name.lower().split())
                score = len(common_words)
                
                if score > best_score and score > 0:
                    best_score = score
                    best_match = (pdf_name, pdf_path)
            
            if best_match:
                mapped_files.append((presentation, best_match[1]))
                self.logger.debug(f"≈ Mapeado (parcial): {presentation} -> {best_match[1].name}")
            else:
                self.missing_pdfs.append(presentation)
                self.logger.warning(f"✗ No encontrado: {presentation}")
        
        # Agregar PDFs no mapeados al final
        mapped_names = {path.name for _, path in mapped_files}
        for pdf_name, pdf_path in pdf_files.items():
            if pdf_path.name not in mapped_names:
                mapped_files.append((f"Adicional: {pdf_name}", pdf_path))
                self.logger.debug(f"+ Agregado adicional: {pdf_path.name}")
        
        self.stats['pdfs_unified'] = len(mapped_files) - len(self.missing_pdfs)
        self.stats['pdfs_missing'] = len(self.missing_pdfs)
        
        return mapped_files

    def create_index_page(self, mapped_files: List[Tuple[str, Path]]) -> Optional[Path]:
        """
        Crea una página de índice para el PDF unificado

        Args:
            mapped_files: Lista de archivos mapeados

        Returns:
            Ruta del archivo de índice creado o None si no se pudo crear
        """
        if not REPORTLAB_AVAILABLE:
            self.logger.warning("ReportLab no disponible, omitiendo página de índice")
            return None

        self.logger.info("Creando página de índice...")

        try:
            # Crear archivo temporal para el índice
            index_path = self.output_dir / "temp_index.pdf"

            # Configurar documento
            doc = SimpleDocTemplate(str(index_path), pagesize=A4)
            styles = getSampleStyleSheet()
            story = []

            # Título
            title = Paragraph("ÍNDICE DE PRESENTACIONES", styles['Title'])
            story.append(title)
            story.append(Spacer(1, 0.5*inch))

            # Subtítulo
            subtitle = Paragraph("UBA FCE - Auditoría y Seguridad Informática", styles['Heading2'])
            story.append(subtitle)
            story.append(Spacer(1, 0.3*inch))

            # Fecha
            fecha = Paragraph(f"Generado el: {datetime.now().strftime('%d/%m/%Y %H:%M')}", styles['Normal'])
            story.append(fecha)
            story.append(Spacer(1, 0.5*inch))

            # Lista de presentaciones
            current_page = 2  # La página de índice es la 1

            for i, (presentation_name, pdf_path) in enumerate(mapped_files, 1):
                try:
                    # Leer PDF para contar páginas
                    reader = PdfReader(str(pdf_path))
                    num_pages = len(reader.pages)

                    # Agregar entrada al índice
                    entry_text = f"{i}. {presentation_name} ........................ Página {current_page}"
                    entry = Paragraph(entry_text, styles['Normal'])
                    story.append(entry)

                    current_page += num_pages

                except Exception as e:
                    self.logger.warning(f"Error leyendo {pdf_path.name}: {e}")
                    entry_text = f"{i}. {presentation_name} ........................ Error"
                    entry = Paragraph(entry_text, styles['Normal'])
                    story.append(entry)

            # Generar PDF
            doc.build(story)

            self.logger.info(f"Página de índice creada: {index_path}")
            return index_path

        except Exception as e:
            self.logger.error(f"Error creando página de índice: {e}")
            return None

    def unify_pdfs(self, mapped_files: List[Tuple[str, Path]]) -> bool:
        """
        Unifica los archivos PDF en el orden especificado

        Args:
            mapped_files: Lista de archivos mapeados en orden

        Returns:
            True si la unificación fue exitosa, False en caso contrario
        """
        self.logger.info("Iniciando unificación de PDFs...")

        try:
            # Crear nombre del archivo de salida
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_filename = f"UBA_FCE_Auditoria_Seguridad_Presentaciones_Unificadas_{timestamp}.pdf"
            output_path = self.output_dir / output_filename

            # Crear escritor de PDF
            writer = PdfWriter()

            # Crear página de índice si es posible
            index_path = self.create_index_page(mapped_files)
            if index_path and index_path.exists():
                try:
                    index_reader = PdfReader(str(index_path))
                    for page in index_reader.pages:
                        writer.add_page(page)
                    self.logger.info("Página de índice agregada")
                except Exception as e:
                    self.logger.warning(f"Error agregando índice: {e}")

            # Procesar cada PDF
            current_page = len(writer.pages)  # Páginas ya agregadas (índice)

            with tqdm(total=len(mapped_files), desc="Unificando PDFs", unit="archivo") as pbar:
                for presentation_name, pdf_path in mapped_files:
                    pbar.set_description(f"Procesando: {pdf_path.name[:30]}...")

                    try:
                        # Leer PDF
                        reader = PdfReader(str(pdf_path))
                        num_pages = len(reader.pages)

                        # Agregar marcador para esta presentación
                        if hasattr(writer, 'add_outline_item'):  # pypdf
                            writer.add_outline_item(presentation_name, current_page)
                        elif hasattr(writer, 'addBookmark'):  # PyPDF2
                            writer.addBookmark(presentation_name, current_page)

                        # Agregar todas las páginas
                        for page in reader.pages:
                            writer.add_page(page)

                        # Actualizar estadísticas
                        self.unified_pdfs.append((presentation_name, pdf_path.name, num_pages))
                        self.stats['total_pages'] += num_pages
                        current_page += num_pages

                        self.logger.debug(f"✓ Agregado: {pdf_path.name} ({num_pages} páginas)")

                    except Exception as e:
                        self.logger.error(f"Error procesando {pdf_path.name}: {e}")
                        continue

                    pbar.update(1)

            # Escribir archivo final
            self.logger.info(f"Escribiendo archivo unificado: {output_filename}")
            with open(output_path, 'wb') as output_file:
                writer.write(output_file)

            # Limpiar archivo temporal de índice
            if index_path and index_path.exists():
                try:
                    index_path.unlink()
                except:
                    pass

            self.logger.info(f"✓ PDF unificado creado exitosamente: {output_path}")
            self.logger.info(f"Total de páginas: {self.stats['total_pages']}")

            return True

        except Exception as e:
            self.logger.error(f"Error durante la unificación: {e}")
            return False

    def generate_report(self):
        """Genera un reporte detallado de la unificación"""
        self.logger.info("\n" + "="*60)
        self.logger.info("REPORTE DE UNIFICACIÓN DE PDFs")
        self.logger.info("="*60)

        # Estadísticas generales
        self.logger.info(f"Total de archivos PDF encontrados: {self.stats['total_pdfs_found']}")
        self.logger.info(f"Archivos unificados: {self.stats['pdfs_unified']}")
        self.logger.info(f"Archivos faltantes: {self.stats['pdfs_missing']}")
        self.logger.info(f"Total de páginas en PDF unificado: {self.stats['total_pages']}")

        # Tasa de éxito
        if self.stats['total_pdfs_found'] > 0:
            success_rate = (self.stats['pdfs_unified'] / self.stats['total_pdfs_found']) * 100
            self.logger.info(f"Tasa de unificación: {success_rate:.1f}%")

        # Archivos unificados
        if self.unified_pdfs:
            self.logger.info(f"\n✓ ARCHIVOS UNIFICADOS ({len(self.unified_pdfs)}):")
            for i, (name, filename, pages) in enumerate(self.unified_pdfs, 1):
                self.logger.info(f"  {i:2d}. {name}")
                self.logger.info(f"      Archivo: {filename} ({pages} páginas)")

        # Archivos faltantes
        if self.missing_pdfs:
            self.logger.info(f"\n✗ PRESENTACIONES NO ENCONTRADAS ({len(self.missing_pdfs)}):")
            for missing in self.missing_pdfs:
                self.logger.info(f"  • {missing}")

        self.logger.info("\n" + "="*60)

    def run_unification(self) -> bool:
        """
        Ejecuta el proceso completo de unificación

        Returns:
            True si la unificación fue exitosa, False en caso contrario
        """
        try:
            # Paso 1: Leer cronograma
            presentation_order = self.read_cronograma()

            # Paso 2: Buscar archivos PDF
            pdf_files = self.find_pdf_files()

            if not pdf_files:
                self.logger.error("No se encontraron archivos PDF para unificar")
                return False

            # Paso 3: Mapear presentaciones a archivos
            mapped_files = self.map_presentations_to_files(presentation_order, pdf_files)

            if not mapped_files:
                self.logger.error("No se pudieron mapear presentaciones a archivos PDF")
                return False

            # Paso 4: Unificar PDFs
            success = self.unify_pdfs(mapped_files)

            # Paso 5: Generar reporte
            self.generate_report()

            return success

        except Exception as e:
            self.logger.error(f"Error durante la unificación: {e}")
            return False


def main():
    """Función principal del script"""
    parser = argparse.ArgumentParser(
        description="Unifica archivos PDF según cronograma del curso",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Ejemplos de uso:
  python pdf_unifier.py
  python pdf_unifier.py --cronograma "mi_cronograma.xlsx" --pdf-dir "mis_pdfs"
  python pdf_unifier.py --log-level DEBUG
        """
    )

    parser.add_argument(
        "--cronograma", "-c",
        default="Cronograma_del_Curso.xlsx",
        help="Ruta al archivo Excel del cronograma (default: 'Cronograma_del_Curso.xlsx')"
    )

    parser.add_argument(
        "--pdf-dir", "-p",
        default="Presentaciones",
        help="Directorio con archivos PDF (default: 'Presentaciones')"
    )

    parser.add_argument(
        "--output-dir", "-o",
        default="Presentaciones/Unificacion para validacion manual",
        help="Directorio de salida (default: 'Presentaciones/Unificacion para validacion manual')"
    )

    parser.add_argument(
        "--log-level", "-l",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default="INFO",
        help="Nivel de logging (default: INFO)"
    )

    args = parser.parse_args()

    try:
        # Crear unificador
        unifier = PDFUnifier(
            cronograma_path=args.cronograma,
            pdf_dir=args.pdf_dir,
            output_dir=args.output_dir,
            log_level=args.log_level
        )

        # Ejecutar unificación
        success = unifier.run_unification()

        # Código de salida basado en resultados
        if success:
            print(f"\n🎉 ¡Unificación completada exitosamente!")
            print(f"📁 Archivo unificado guardado en: {args.output_dir}")
            sys.exit(0)
        else:
            print(f"\n❌ La unificación falló. Revisa los logs para más detalles.")
            sys.exit(1)

    except KeyboardInterrupt:
        print("\n\nUnificación interrumpida por el usuario.")
        sys.exit(130)

    except Exception as e:
        print(f"\nError fatal: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
