================================================================================
PRESENTACIÓN: UBA FCE - Auditoria y Seguridad - Detección y respuesta a incidentes de Seguridad.pptx.pdf
================================================================================
Formato: .pdf
Total Páginas/Diapositivas: 13
Tamaño del archivo: 423,526 bytes
Fecha de extracción: 2025-06-14 21:40:26
Archivo original modificado: 2025-06-14 20:56:33

--- PÁGINA/DIAPOSITIVA 1 ---
Contenido de Texto:
  Seguridad Informática y
Principios de Auditoría
Detección y respuesta a
incidentes de Seguridad
Profesor Pablo <PERSON>genes:
  Imágenes detectadas: 1

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 2 ---
Contenido de Texto:
  ¿Que es un incidente de seguridad?
Un incidente o evento de seguridad es cualquier vulneración digital
o física que pone en peligro la confidencialidad, integridad o
disponibilidad de los sistemas de información o datos sensibles de
una organización.
● Ransomware
● Phishing e ingeniería social
● Ataques DDoS
● Ataques a la cadena de suministro
● Amenazas internas
● Ataques de escalada de privilegios
● Ataques de intermediario
UBA FCE – Auditoría y Seguridad de los Sistemas de Información Slide 2

Tablas:
  Tabla 1:
    
    ¿Que es un incidente de seguridad?

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 3 ---
Contenido de Texto:
  Eventos (Log)
• Cualquier ocurrencia que se registra dentro de un sistema o red.
Esto puede incluir acciones rutinarias como el inicio de sesión de un usuario, el acceso a
un archivo, o los cambios de configuración del sistema.
• NO necesariamente indican un problema de seguridad. Son
simplemente actividades que se monitorean para tener un registro
de lo que está sucediendo en el sistema.
• La mayoría de los eventos son benignos y no requieren
intervención. Sin embargo, algunos eventos pueden ser parte de un
patrón que, si se observa en conjunto, puede señalar un problema.
UBA FCE – Auditoría y Seguridad de los Sistemas de Información Slide 3

Tablas:
  Tabla 1:
    
    Eventos (Log)

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 4 ---
Contenido de Texto:
  Eventos - ejemplo
UBA FCE – Auditoría y Seguridad de los Sistemas de Información Slide 4

Imágenes:
  Imágenes detectadas: 1

Tablas:
  Tabla 1:
    
    Eventos - ejemplo
    

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 5 ---
Contenido de Texto:
  Principales Herramientas de Monitoreo
• SIEM (Security Information and Event Management / Gestión de Eventos e
Información de Seguridad)
• WAF (Web Application Firewall) / Firewall
• IDS (Intrusion Detection System / Sistema de detección de intrusiones)
• IPS (Intrusion Prevention System / sistema de prevención de intrusiones)
• EDR / XDR (Detección avanzada de amenazas y respuesta automatizada)
Las herramientas de monitoreo son esenciales en la detección de
incidentes. Ya que en la correlación de eventos se pueden encontrar
comportamientos anómalos que son un potencial incidente
UBA FCE – Auditoría y Seguridad de los Sistemas de Información Slide 5

Tablas:
  Tabla 1:
    
    Principales Herramientas de Monitoreo
    • SIEM (Security Information and Event Management / Gestión de Eventos e

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 6 ---
Contenido de Texto:
  Incidentes - ejemplos
• Acceso no autorizado
• Ataque de malware
• Intento de exfiltración de datos
UBA FCE – Auditoría y Seguridad de los Sistemas de Información Slide 6

Imágenes:
  Imágenes detectadas: 1

Tablas:
  Tabla 1:
    
    Incidentes - ejemplos

  Tabla 2:
    None |  | None
    None |  | None
    • Acceso no autorizado
• Ataque de malware
• Intento de exfiltración de datos |  | 
    None |  | None

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 7 ---
Contenido de Texto:
  Proceso de manejo de incidentes
Un proceso de manejo de incidentes está diseñado para permitir que
una organización restablezca el servicio cuando un servicio está
inactivo o degradado.
UBA FCE – Auditoría y Seguridad de los Sistemas de Información Slide 7

Imágenes:
  Imágenes detectadas: 1

Tablas:
  Tabla 1:
    
    Proceso de manejo de incidentes

  Tabla 2:
    
    

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 8 ---
Contenido de Texto:
  Ciclo de vida de un Incidente
1. Detección
2. Análisis
3. Contención
4. Erradicación
5. Recuperación
6. Lecciones aprendidas
UBA FCE – Auditoría y Seguridad de los Sistemas de Información Slide 8

Imágenes:
  Imágenes detectadas: 1

Tablas:
  Tabla 1:
    
    Ciclo de vida de un Incidente
    
    

  Tabla 2:
    
    Slide 8

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 9 ---
Contenido de Texto:
  Ciclo de vida de un Incidente
1. Detección: ¿cómo se va a detectar el incidente?
2. Análisis: ¿cómo se va a determinar el alcance del impacto?
3. Contención: ¿cómo se va a aislar el incidente para limitar el
alcance?
4. Erradicación: ¿cómo se va a eliminar la amenaza del entorno?
5. Recuperación: ¿cómo se va a conseguir que el sistema o recurso
afectado vuelva a ser productivo?
UBA FCE – Auditoría y Seguridad de los Sistemas de Información Slide 9

Tablas:
  Tabla 1:
    
    Ciclo de vida de un Incidente
    
    1. Detección: ¿cómo se va a detectar el incidente?
2. Análisis: ¿cómo se va a determinar el alcance del impacto?
3. Contención: ¿cómo se va a aislar el incidente para limitar el
alcance?
4. Erradicación: ¿cómo se va a eliminar la amenaza del entorno?
5. Recuperación: ¿cómo se va a conseguir que el sistema o recurso
afectado vuelva a ser productivo?

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 10 ---
Contenido de Texto:
  Respuesta a incidentes
UBA FCE – Auditoría y Seguridad de los Sistemas de Información Slide 10

Imágenes:
  Imágenes detectadas: 1

Tablas:
  Tabla 1:
    
    Respuesta a incidentes

  Tabla 2:
    None |  | None
    None |  | None
     |  | 

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 11 ---
Contenido de Texto:
  Respuesta a incidentes - Buenas prácticas
UBA FCE – Auditoría y Seguridad de los Sistemas de Información Slide 11

Imágenes:
  Imágenes detectadas: 1

Tablas:
  Tabla 1:
    
    Respuesta a incidentes - Buenas prácticas

  Tabla 2:
    
    
    

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 12 ---
Contenido de Texto:
  Respuesta a incidentes - Resumen
• Esta alerta es un problema real. ¿Y ahora qué?
• Minimizar y limitar el alcance del incidente
• Proteger la experiencia del usuario externo lo más rápido posible
• Entender que la contención es siempre mejor que la resolución
• Marco para tomar decisiones bajo presión
• Utilizar las herramientas de contención correctas
• Para una mitigación rápida, el factor más importante es tomar
buenas decisiones.
• Detectar a tiempo, cada minuto cuenta
UBA FCE – Auditoría y Seguridad de los Sistemas de Información Slide 12

Tablas:
  Tabla 1:
    
    Respuesta a incidentes - Resumen
    
    • Esta alerta es un problema real. ¿Y ahora qué?
• Minimizar y limitar el alcance del incidente
• Proteger la experiencia del usuario externo lo más rápido posible
• Entender que la contención es siempre mejor que la resolución
• Marco para tomar decisiones bajo presión
• Utilizar las herramientas de contención correctas
• Para una mitigación rápida, el factor más importante es tomar
buenas decisiones.
• Detectar a tiempo, cada minuto cuenta

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 13 ---
Contenido de Texto:
  UBA FCE – Auditoría y Seguridad de los Sistemas de Información Slide 13

Imágenes:
  Imágenes detectadas: 1

--------------------------------------------------
