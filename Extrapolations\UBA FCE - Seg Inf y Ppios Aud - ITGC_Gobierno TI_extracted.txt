================================================================================
PRESENTACIÓN: UBA FCE - Seg Inf y Ppios Aud - ITGC_Gobierno TI.pptx
================================================================================
Formato: .pptx
Total Páginas/Diapositivas: 12
Tamaño del archivo: 414,868 bytes
Fecha de extracción: 2025-06-14 21:40:29
Archivo original modificado: 2025-06-14 20:55:39

--- PÁGINA/DIAPOSITIVA 1 ---
Contenido de Texto:
  Seguridad Informática y Principios de Auditoría Gobierno de IT y Controles Generales de TI (ITGC)
  Profesor <PERSON>

Imágenes:
  Imagen detectada: Picture 3

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 2 ---
Contenido de Texto:
  Slide 2
  Gobierno de IT (Tecnologías de Información)
  Empresas y sus partes interesadas
Las empresas existen para crear valor para sus partes interesadas (Stakeholders). 
En consecuencia, cualquier organización tendrá la creación de valor como un objetivo de gobernabilidad. La creación de valor significa obtener beneficios a un costo de recursos óptimo mientras se optimizan los riesgos.

Imágenes:
  Imagen detectada: Picture 2

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 3 ---
Contenido de Texto:
  Slide 3
  Gobierno de IT (Tecnologías de Información)
  El Gobierno de IT hace referencia al conjunto de estructuras y procesos que una organización gestiona, de manera eficiente y segura, para administrar y dirigir sus sistemas, que deben estar alineados y considerar además el cumplimiento de los objetivos estrategicos de la Alta Dirección (Directorio y Accionistas) y Grupos de interes Internos (el Negocio) o Externos (clientes, proveedores, otros) afectados por la actividad de la organización.

Elementos clave del Gobierno de IT:
Alineación estratégica
Roles y responsabilidad (para la gestión de las TI)
Gestión de riesgos
Gestión de recursos
Cumplimiento de leyes y regulaciones

Imágenes:
  Imagen detectada: Imagen 1

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 4 ---
Contenido de Texto:
  Slide 4
  Gobierno de IT (Tecnologías de Información)
  Gobierno de IT vs Gestión de IT

El gobierno garantiza que las necesidades, las condiciones y las opciones de las partes interesadas se evalúen para determinar las metas empresariales equilibradas y acordadas que se deben alcanzar; establece la dirección mediante la priorización y la toma de decisiones; y monitorea el desempeño y el cumplimiento de acuerdo con la dirección y los objetivos acordados.

La gestión (gerencia) planea, construye, ejecuta y monitorea las actividades en consonancia con la dirección establecida por el órgano de gobierno para alcanzar las metas empresariales.

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 5 ---
Contenido de Texto:
  Slide 5
  Gobierno de IT (Tecnologías de Información)
  Principales funciones del Gobierno de IT









El Gobierno corporativo y el Gobierno de IT son ambos componentes del Gobierno global de una organización.

Imágenes:
  Imagen detectada: Imagen 4

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 6 ---
Contenido de Texto:
  Slide 6
  Gobierno de IT (Tecnologías de Información)
  Principales Marcos de trabajo y buenas prácticas
ISO/IEC 38500:2008: Corporate governance of information technology, (basado en AS8015-2005), define un marco de trabajo para el gobierno de TI 
COBIT (Control Objectives for Information and related Technology): Es un modelo de referencia que describe 34 procesos relacionados con TI y que son comunes a todas las organizaciones. 
AS8015-2005: Estándar australiano para el gobierno corporativo de la tecnología de la información y las comunicación

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 7 ---
Contenido de Texto:
  Slide 7
  Actividades de Gobierno del área de Sistemas
  En resumen:
Estructura y dependencia del área de Sistemas – segregación de funciones
Plan estratégico de sistemas – definición y seguimiento
Gestión de riesgos de TI – identificación de todos los riesgos
Definición de Políticas y procedimientos - definición de tareas y controles
Gestión de relaciones con terceros – proveedores de servicios
Cumplimiento – certificaciones leyes y regulaciones aplicables
Métricas de evaluación TI – efectividad del gobierno

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 8 ---
Contenido de Texto:
  Controles Generales de TI (ITGC)
  Slide 8
  EEFF
  Controles a nivel entidad (indirectos)
  Segregación de funciones

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 9 ---
Contenido de Texto:
  Controles Generales de TI (ITGC)
  Slide 9
  Controles a nivel entidad (indirectos)
  Políticas, procedimientos y actividades de control que se relacionan con varios sistemas/aplicaciones y soportan el funcionamiento efectivo de los controles a nivel de transacción.

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 10 ---
Contenido de Texto:
  Slide 10
  Controles Generales de TI (ITGC)
  Los ITGC son relevantes ya que constituyen el ambiente de generación y procesamiento de:


Cálculos realizados por las aplicaciones
Reportes generados por las aplicaciones
Controles automáticos
Seguridad lógica (incluyendo segregación de funciones)
Interfases entre aplicaciones

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 11 ---
Contenido de Texto:
  Slide 11
  Controles Generales de TI (CGTI)
  Riesgos que surgen del ambiente de TI
  Acceso al software y a los datos
Accesos no autorizados a las aplicaciones por parte de usuarios de negocio
Accesos no autorizados a las aplicaciones y a los datos por parte de usuarios de TI
Cambios no autorizados a los datos
  Cambios a los programas
Cambios no solicitados por el negocio
Cambios que introducen errores en las aplicaciones
Cambios directos y no autorizados a los programas
Cambios directos y no autorizados a la configuración del sistema
Actualizaciones inadecuadas de sistemas operativos y bases de datos
  Desarrollo de sistemas
Los sistemas implementados procesan datos de manera incorrecta debido a problemas de codificación o configuración
Errores al migrar registros de transacciones y/o datos maestros
  Operaciones computarizadas
Fallas en los sistemas que causan pérdida de datos o transacciones o incapacidad para acceder a ellos según se requiera
Intervención manual inapropiada o fallas en el procesamiento de trabajos programados

--------------------------------------------------

--- PÁGINA/DIAPOSITIVA 12 ---
Contenido de Texto:
  Slide 12
  Controles Generales de TI (CGTI)
  Acceso al software y a los datos
  Seguridad Cloud

--------------------------------------------------
